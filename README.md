# Multi-Scraper Dashboard

A web-based dashboard for running multiple product scrapers simultaneously and viewing aggregated results.

![Multi-Scraper Dashboard](screenshot.png)

## Overview

This project provides a unified interface for searching products across multiple supplier websites. It allows users to enter a product code and simultaneously query multiple supplier scrapers, displaying the results in a clean, organized format with helpful statistics.

## Features

- **Unified Search Interface**: Search across all suppliers with a single query
- **Real-time Status Updates**: See the progress of each scraper in real-time
- **Comprehensive Statistics**: View aggregated statistics about the search results
- **Intelligent Results Grouping**: Results are grouped by product code and sorted by price
- **Advanced Filtering**: Filter results by product code, supplier, brand, or free text search
- **Error Diagnostics**: Detailed error logs for failed scrapers help diagnose issues
- **Customizable Display**: Toggle additional columns on/off as needed
- **Price Comparison**: Easily identify the lowest-priced option for each product
- **Total Cost Calculation**: See the total cost including exchange values
- **Responsive Design**: Works on desktop and mobile devices
- **Network Resilience Testing**: Test scrapers under various network conditions to ensure reliability

## Architecture

The application consists of three main components:

1. **Individual Scrapers**: Located in `src/scrapers/[supplier-name]`, each scraper is responsible for extracting product data from a specific supplier website.

2. **Multi-Scraper Orchestrator**: The `multi-scraper.js` file coordinates running all scrapers in parallel and aggregating their results.

3. **Web Interface**: The `web-scraper.js` file provides a web server and Socket.IO implementation for the user interface.

## Technical Implementation

### Backend

- **Express.js Server**: Hosts the web interface and API endpoints
- **Socket.IO**: Provides real-time communication between server and client
- **Parallel Processing**: Runs all scrapers simultaneously using Promise.all
- **Error Handling**: Robust error handling ensures one failed scraper doesn't affect others

### Frontend

- **Modern UI**: Clean, responsive interface built with Tailwind CSS
- **Real-time Updates**: Named status tiles show progress of each scraper
- **Interactive Filtering**: Multiple filter options for precise result refinement
- **Data Visualization**: Clear presentation of statistics and results
- **Grouped Results**: Products organized by product code with visual separators
- **Price Sorting**: Automatic sorting by price within each product group
- **Visual Indicators**: Lowest price highlighting and brand information
- **Error Diagnostics**: Floating action button with detailed error logs
- **Customizable Display**: Toggle columns on/off to customize the view
- **Responsive Tables**: Properly formatted data with appropriate styling

## How It Works

1. **User Enters Product Code**: The user enters a product code in the search bar
2. **Scrapers Run in Parallel**: All scrapers run simultaneously to search for the product
3. **Real-time Updates**: The UI shows the status of each scraper as it runs
4. **Results Aggregation**: Results from all scrapers are combined and displayed
5. **Statistics Generation**: Summary statistics are calculated and displayed

## Interface Features

### Scraper Status Tiles
- **Real-time Status Updates**: Each scraper has a dedicated status tile showing its current state
- **Scraper Name Display**: Each tile clearly displays the scraper name for easy identification
- **Status Indicators**: Visual indicators show whether a scraper is loading, successful, or has failed
- **Product Count**: Successful scrapers show the number of products found

### Error Handling
- **Error Notification**: A floating action button appears when any scraper encounters an error
- **Detailed Error Logs**: Clicking the error button displays detailed logs for each failed scraper
- **Scraper-specific Logs**: Error logs are separated by scraper for easier troubleshooting
- **Complete Context**: The error modal includes both the error message and the scraper's logs

### Results Table
- **Product Code Grouping**: Results are grouped by product code for easier comparison
- **Most Frequent Brand Display**: Each product code group shows the most common brand
- **Price-based Sorting**: Within each group, products are sorted by price in ascending order
- **Lowest Price Indicator**: The lowest-priced item in each group is highlighted
- **Total Cost Calculation**: For items with exchange values, the total cost is calculated and displayed
- **Conditional Brand Column**: A toggle allows showing/hiding the detailed Brand column
- **Visual Separators**: Clear visual separation between different product code groups

### Filtering and Search
- **Text Search**: Filter results by any text field (name, brand, product code, etc.)
- **Product Code Filter**: Quickly filter to see only a specific product code
- **Supplier Filter**: Filter results by supplier
- **Brand Filter**: Filter results by brand
- **Combined Filters**: All filters can be used together for precise results

## Scrapers Included

The following supplier scrapers are included:

- **AUTONET**: Scrapes products from Autonet
- **AUTOTOTAL**: Scrapes products from Autototal (uses modular orchestrator architecture)
- **INTERCARS**: Scrapes products from Intercars
- **AUTOPARTNER**: Scrapes products from Autopartner
- **MATEROM**: Scrapes products from Materom
- **ELIT**: Scrapes products from Elit
- **BARDI**: Scrapes products from Bardi

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```
   git clone [repository-url]
   cd [repository-directory]
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the server:
   ```
   npm start
   ```

4. Open your browser and navigate to:
   ```
   http://localhost:3000
   ```

### Usage

1. Enter a product code in the search bar (e.g., JRP675)
2. Click "Search" to start the scrapers
3. Watch the real-time status updates as scrapers run
   - Each scraper has its own status tile showing progress
   - Successful scrapers show the number of products found
   - Failed scrapers trigger an error notification
4. View the aggregated results and statistics
   - Results are grouped by product code
   - Within each group, products are sorted by price (lowest first)
   - The most common brand is displayed for each product code
   - Total cost is calculated for items with exchange values
5. Use the interface features to analyze results:
   - Toggle the Brand column on/off using the switch
   - Use filters to narrow down results by product code, supplier, or brand
   - Use the text search to find specific items
   - Click the error notification button (if present) to view detailed error logs

## Command Line Usage

### Multi-Scraper

You can run the multi-scraper from the command line:

```
node multi-scraper.js [product-code]
```

This will run all scrapers for the specified product code and display the results in the console.

### Network Testing

The project includes a network testing framework to evaluate scraper performance under different network conditions. This helps ensure scrapers are resilient to poor connectivity scenarios.

To run network tests for a specific scraper:

```
node test-network.js <provider> <product-code>
```

For example:
```
node test-network.js autonet **********
```

Available providers:
- autonet
- autototal
- intercars
- autopartner
- materom
- elit
- bardi

The network test will run the specified scraper under 6 different network conditions:
1. Fast 4G (4 MB/s download, 20ms latency)
2. Average 3G (1 MB/s download, 200ms latency)
3. Slow Connection (100 KB/s download, 800ms latency)
4. Terrible Connection (30 KB/s download, 2000ms latency)
5. Packet Loss (10%) (200 KB/s download, 500ms latency, 10% packet loss)
6. Nightmare Connection (5 KB/s download, 3000ms latency, 20% packet loss, 500ms jitter)

After running all tests, a comprehensive report is generated showing:
- Success rate across all network conditions
- Performance metrics (fastest/slowest test, speed difference)
- Product consistency comparison across network conditions
- Recommendations for improving scraper resilience

## Development

### Project Structure

```
├── public/                 # Static web assets
│   ├── index.html          # Main HTML file
│   ├── script.js           # Client-side JavaScript
│   └── styles.css          # CSS styles (not used with Tailwind)
├── src/                    # Source code
│   ├── scrapers/           # Individual scrapers
│   │   ├── autonet/        # Autonet scraper
│   │   ├── autototal/      # Autototal scraper (modular architecture)
│   │   │   ├── config.js   # Configuration constants
│   │   │   ├── auth.js     # Authentication logic
│   │   │   ├── navigation.js # Page navigation and pagination
│   │   │   ├── extractors.js # Data extraction logic
│   │   │   ├── orchestrator.js # Main coordination logic (active)
│   │   │   └── index.js    # Legacy implementation (deprecated)
│   │   └── ...             # Other scrapers
│   └── shared/             # Shared utilities
├── multi-scraper.js        # Multi-scraper orchestrator
├── web-scraper.js          # Web server and Socket.IO implementation
├── network-test-runner.js  # Network testing framework
├── test-network.js         # CLI for network testing
└── package.json            # Project dependencies
```

### AUTOTOTAL Modular Architecture

The AUTOTOTAL scraper has been refactored into a modular architecture for better maintainability and code organization:

- **orchestrator.js** (Active): Main entry point that coordinates all scraping operations
- **config.js**: Contains all configuration constants and settings
- **auth.js**: Handles authentication and login logic
- **navigation.js**: Manages page navigation and pagination
- **extractors.js**: Contains data extraction and parsing logic
- **index.js** (Deprecated): Legacy monolithic implementation, no longer used

**Note**: The system now uses `orchestrator.js` instead of the deprecated `index.js` file for AUTOTOTAL scraping operations.

### Adding a New Scraper

1. Create a new folder in `src/scrapers/[supplier-name]`
2. Implement the scraper following the existing pattern (consider using the modular approach like AUTOTOTAL)
3. Add the scraper to the list in `multi-scraper.js`
4. Add the scraper to the list in `web-scraper.js`

## Key Technical Features

### Real-time Updates with Socket.IO

The application uses Socket.IO to provide real-time updates as scrapers run. This allows users to see the progress of each scraper without having to wait for all scrapers to complete.

### Dynamic UI Components

The UI dynamically creates and updates status indicators for each scraper, ensuring that users always have up-to-date information about the scraping process.

### Error Handling

The application includes robust error handling to ensure that if one scraper fails, the others continue to run and provide results.

### Network Resilience Testing

The project includes a modular network testing framework that can evaluate any scraper's performance under various network conditions:

- **Configurable Network Conditions**: Tests scrapers under different bandwidth, latency, and packet loss scenarios
- **Cross-Condition Comparison**: Compares product results across different network conditions to identify discrepancies
- **Performance Metrics**: Generates detailed performance statistics to identify bottlenecks
- **Resilience Analysis**: Provides recommendations for improving scraper resilience based on test results
- **Modular Design**: Can be used with any scraper in the system through a simple command-line interface

### Responsive Design

The UI is built with Tailwind CSS and is fully responsive, working well on both desktop and mobile devices.

## License

[Your License Here]

## Acknowledgements

- [Tailwind CSS](https://tailwindcss.com/) for the UI styling
- [Socket.IO](https://socket.io/) for real-time communication
- [Express.js](https://expressjs.com/) for the web server
- [Puppeteer](https://pptr.dev/) for web scraping
