/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

header p {
    color: #7f8c8d;
}

/* Search form */
.search-container {
    margin-bottom: 30px;
}

.input-group {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
}

input[type="text"] {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 16px;
}

button {
    padding: 12px 20px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

/* Loading spinner */
.hidden {
    display: none;
}

#loading {
    text-align: center;
    margin: 40px 0;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid #3498db;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#progress-container {
    max-width: 600px;
    margin: 20px auto;
    padding: 10px;
    background-color: #f1f1f1;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
}

#progress-log {
    font-family: monospace;
    font-size: 14px;
    white-space: pre-wrap;
}

/* Results container */
#results-container {
    margin-top: 30px;
}

.stats-container {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.stat-card {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-card h3 {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 10px;
}

.stat-card p {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
}

.supplier-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.supplier-badge {
    background-color: #e9f7fe;
    color: #3498db;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 14px;
}

/* Results table */
.results-table-container {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.filter-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.filter-container input,
.filter-container select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.filter-container input {
    flex: 1;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: 500;
}

tr:hover {
    background-color: #f5f7fa;
}

/* Error container */
#error-container {
    background-color: #fee;
    border-left: 4px solid #e74c3c;
    padding: 15px;
    border-radius: 4px;
    margin-top: 20px;
}

#error-container h2 {
    color: #e74c3c;
    margin-bottom: 10px;
}
