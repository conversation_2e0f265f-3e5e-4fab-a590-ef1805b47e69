{"name": "autonet-scrapper", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node web-scraper.js", "scrape": "node multi-scraper.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"autonet-scrapper": "file:", "autoprefixer": "^10.4.21", "dotenv": "^16.5.0", "express": "^4.21.2", "postcss": "^8.5.3", "puppeteer": "^24.5.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-recaptcha": "^3.6.8", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-extra-plugin-user-preferences": "^2.4.1", "puppeteer-real-browser": "^1.4.2", "socket.io": "^4.8.1", "tailwindcss": "^4.1.6", "yargs": "^17.7.2"}}