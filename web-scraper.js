const express = require('express');
const path = require('path');
const http = require('http');
const { Server } = require('socket.io');
const { runAllScrapers, generateStatistics } = require('./multi-scraper');

// Create Express app
const app = express();
const server = http.createServer(app);
const io = new Server(server);
const port = 3000;

// Middleware for parsing JSON and URL-encoded data
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files from the 'public' directory
app.use(express.static(path.join(__dirname, 'public')));

// Serve logo.png from the root directory
app.get('/logo.png', (req, res) => {
  res.sendFile(path.join(__dirname, 'logo.png'));
});

// Route for the home page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Socket.IO connection handler
io.on('connection', (socket) => {
  console.log('Client connected');

  socket.on('disconnect', () => {
    console.log('Client disconnected');
  });
});

// Create a modified version of runAllScrapers that sends updates via Socket.IO
async function runAllScrapersWithUpdates(productCode, socket) {
  console.log(`Starting scraper for product code: ${productCode}`);

  // Define all scrapers with their corresponding functions and names
  const scrapers = [
    { name: 'AUTONET', fn: require('./src/scrapers/autonet/index').runAutonetScraper },
    { name: 'AUTOTOTAL', fn: require('./src/scrapers/autototal/index').runAutototalScraperModular },
    { name: 'INTERCARS', fn: require('./src/scrapers/intercars/index').runIntercarsScraper },
    { name: 'AUTOPARTNER', fn: require('./src/scrapers/autopartner/index').runAutopartnerScraper },
    { name: 'MATEROM', fn: require('./src/scrapers/materom/index').runMateromScraper },
    { name: 'ELIT', fn: require('./src/scrapers/elit/index').runElitScraper },
    { name: 'BARDI', fn: require('./src/scrapers/bardi/index').runBardiScraper }
  ];

  // Emit initial status for all scrapers
  scrapers.forEach(scraper => {
    socket.emit('scraper-status', {
      supplier: scraper.name,
      status: 'loading',
      message: 'Starting...'
    });
  });

  // Run all scrapers in parallel using Promise.allSettled
  const results = {};

  // Create an array of promises that will emit status updates
  const scraperPromises = scrapers.map(async (scraper) => {
    // Create an array to store this scraper's logs
    const scraperLogs = [];

    // Create a custom logger for this scraper
    const scraperLogger = (message) => {
      // Add timestamp
      const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
      const logMessage = `[${timestamp}] ${message}`;

      // Store in the scraper's logs
      scraperLogs.push(logMessage);

      // Also log to the main console
      console.log(`[${scraper.name}] ${message}`);

      // Send to the client
      socket.emit('log', `[${scraper.name}] ${message}`);
    };

    // Intercept console.log and console.error during scraper execution
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;

    // Override console.log and console.error to capture scraper logs
    console.log = function() {
      const args = Array.from(arguments).join(' ');
      // Only capture logs that are likely from this scraper
      if (args.includes(scraper.name) || args.includes('scraper') || args.includes('Scraper')) {
        scraperLogs.push(args);
      }
      // Call the original console.log
      originalConsoleLog.apply(console, arguments);
    };

    console.error = function() {
      const args = Array.from(arguments).join(' ');
      // Always capture error logs
      scraperLogs.push(`ERROR: ${args}`);
      // Call the original console.error
      originalConsoleError.apply(console, arguments);
    };

    try {
      scraperLogger(`🔍 Starting ${scraper.name} scraper...`);

      const startTime = Date.now();
      // Execute the scraper function
      const result = await scraper.fn(productCode);
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000; // Convert to seconds

      // Restore original console functions
      console.log = originalConsoleLog;
      console.error = originalConsoleError;

      // Check if result is null (indicating an error)
      if (result === null) {
        // Extract error message from the logs if possible
        let errorMessage = `${scraper.name} scraper failed to retrieve data`;

        // Look for error messages in the logs
        const errorLogs = scraperLogs.filter(log =>
          log.includes('Error') ||
          log.includes('error') ||
          log.includes('failed') ||
          log.includes('Failed') ||
          log.includes('TimeoutError') ||
          log.includes('❌')
        );

        if (errorLogs.length > 0) {
          // Use the most detailed error log as the error message
          const detailedErrorLog = errorLogs.find(log => log.includes('TimeoutError') || log.includes('at '));
          if (detailedErrorLog) {
            errorMessage = detailedErrorLog;
          } else {
            errorMessage = errorLogs[0];
          }
        }

        // Log the failure
        scraperLogger(`❌ ${scraper.name} scraper failed with null result`);

        // Emit error status
        socket.emit('scraper-status', {
          supplier: scraper.name,
          status: 'error',
          message: errorMessage,
          logs: scraperLogs.join('\n') // Include the scraper's logs
        });

        socket.emit('log', `❌ ${scraper.name} scraper failed: See error logs for details`);

        results[scraper.name] = {
          supplier: scraper.name,
          success: false,
          error: errorMessage,
          logs: scraperLogs.join('\n'), // Include the scraper's logs
          data: [],
          count: 0,
          duration: duration
        };
      } else {
        // Log success message
        scraperLogger(`✅ Completed successfully with ${result ? result.length : 0} products.`);

        // Emit success status
        socket.emit('scraper-status', {
          supplier: scraper.name,
          status: 'success',
          message: `Found ${result ? result.length : 0} products`,
          logs: scraperLogs.join('\n') // Include the scraper's logs
        });

        results[scraper.name] = {
          supplier: scraper.name,
          success: true,
          data: result || [],
          count: result ? result.length : 0,
          logs: scraperLogs.join('\n'), // Include the scraper's logs
          duration: duration
        };
      }
    } catch (error) {
      // Restore original console functions
      console.log = originalConsoleLog;
      console.error = originalConsoleError;

      // Log the error
      scraperLogger(`❌ Error: ${error.message}`);
      console.error(`❌ Error in ${scraper.name} scraper:`, error);

      // Emit error status
      socket.emit('scraper-status', {
        supplier: scraper.name,
        status: 'error',
        message: error.message || 'Unknown error',
        logs: scraperLogs.join('\n') // Include the scraper's logs
      });

      results[scraper.name] = {
        supplier: scraper.name,
        success: false,
        error: error.message,
        logs: scraperLogs.join('\n'), // Include the scraper's logs
        data: [],
        count: 0,
        duration: 0
      };
    }
  });

  // Wait for all scrapers to complete
  await Promise.all(scraperPromises);

  return results;
}

// API endpoint to run the scraper
app.post('/api/scrape', async (req, res) => {
  const { productCode } = req.body;

  if (!productCode) {
    return res.status(400).json({ error: 'Product code is required' });
  }

  // Store the socket ID to send updates to the correct client
  const socketId = req.body.socketId;
  const socket = io.sockets.sockets.get(socketId);

  if (!socket) {
    return res.status(400).json({ error: 'Invalid socket connection' });
  }

  // Send initial response to let the client know we've started
  res.json({
    success: true,
    message: 'Scraping started'
  });

  try {
    // Run all scrapers with real-time updates
    const startTime = Date.now();
    const results = await runAllScrapersWithUpdates(productCode, socket);
    const endTime = Date.now();
    const totalDuration = ((endTime - startTime) / 1000).toFixed(2);

    // Generate statistics
    const stats = generateStatistics(results);

    // Send final results
    socket.emit('scraping-complete', {
      success: true,
      totalDuration,
      stats,
      results
    });
  } catch (error) {
    console.error('Error running scrapers:', error);
    socket.emit('scraping-error', {
      success: false,
      error: error.message || 'An error occurred while running the scrapers'
    });
  }
});

// Start the server
server.listen(port, () => {
  console.log(`Web scraper server running at http://localhost:${port}`);
});
