const { sleep, randomDelay } = require('../../shared/utils');
const { saveCookiesToFile, saveSessionUrlToFile } = require('../../shared/session');
const config = require('./config');

/**
 * Checks if the user is logged in and performs login if needed
 * @param {Object} page - Puppeteer page object
 * @returns {Promise<boolean>} - True if logged in successfully
 */
async function ensureLoggedIn(page) {
  try {
    console.log('🔍 Checking if already logged in...');
    await page.waitForSelector(config.SELECTORS.LOGIN.LOGGED_IN, { 
      timeout: config.LOGIN_CHECK_TIMEOUT 
    });
    console.log('✅ Already logged in');
    return true;
  } catch {
    console.log('🔐 Not logged in or session expired. Performing login...');
    return await performLogin(page);
  }
}

/**
 * Performs the login process
 * @param {Object} page - Puppeteer page object
 * @returns {Promise<boolean>} - True if login successful
 */
async function performLogin(page) {
  const isOnLoginPage = await page.$(config.SELECTORS.LOGIN.USERNAME) !== null;

  if (!isOnLoginPage) {
    console.log(`Navigating to login page: ${config.LOGIN_URL}`);
    await page.goto(config.LOGIN_URL, { waitUntil: 'domcontentloaded', timeout: 6000 });
  } else {
    console.log('🔄 Already on the login page.');
  }

  // Enter username
  await page.waitForSelector(config.SELECTORS.LOGIN.USERNAME);
  await page.type(
    config.SELECTORS.LOGIN.USERNAME, 
    process.env.AUTOTOTAL_USER, 
    { delay: randomDelay() }
  );
  await sleep(randomDelay(config.DELAYS.BETWEEN_INPUTS[0], config.DELAYS.BETWEEN_INPUTS[1]));
  
  // Enter password
  await page.type(
    config.SELECTORS.LOGIN.PASSWORD, 
    process.env.AUTOTOTAL_PASSWORD, 
    { delay: randomDelay() }
  );
  await sleep(randomDelay(config.DELAYS.BETWEEN_INPUTS[0], config.DELAYS.BETWEEN_INPUTS[1]));
  
  // Click login button and wait for navigation
  await page.click(config.SELECTORS.LOGIN.LOGIN_BUTTON);
  await page.waitForNavigation({ waitUntil: 'networkidle2' });
  
  // Handle device registration if needed
  if (page.url().includes('/device/check-device')) {
    console.log('📱 Device registration required');
    await page.type(
      config.SELECTORS.LOGIN.DEVICE_NAME, 
      process.env.AUTOTOTAL_DEVICE, 
      { delay: randomDelay() }
    );
    await sleep(randomDelay(config.DELAYS.BETWEEN_INPUTS[0], config.DELAYS.BETWEEN_INPUTS[1]));
    await page.click(config.SELECTORS.LOGIN.DEVICE_SAVE_BUTTON);
    await page.waitForSelector(config.SELECTORS.LOGIN.LOGGED_IN);
    console.log('✅ Device registered successfully');
  } else {
    await page.waitForSelector(config.SELECTORS.LOGIN.LOGGED_IN);
  }
  
  // Save session data
  await saveCookiesToFile(page, config.COOKIES_PATH);
  saveSessionUrlToFile(page.url(), config.SESSION_URL_PATH);
  console.log('✅ Login successful and session saved');
  
  return true;
}

module.exports = {
  ensureLoggedIn,
  performLogin
};
