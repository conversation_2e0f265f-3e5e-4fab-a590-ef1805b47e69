/**
 * Generates a random delay between min and max milliseconds.
 * @param {number} min Minimum delay in ms (default: 100)
 * @param {number} max Maximum delay in ms (default: 200)
 * @returns {number} Random delay time
 */
function randomDelay(min = 100, max = 200) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Pauses execution for a specified number of milliseconds.
 * @param {number} ms Time to sleep in milliseconds
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

module.exports = {
  randomDelay,
  sleep,
};