const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });

// Import the modular version of the scraper
const { runAutototalScraperModular } = require('./orchestrator');

// Keep the original imports for backward compatibility
const { launchBrowser } = require('../../shared/browser');
const {
  saveCookiesToFile,
  loadCookiesFromFile,
  saveSessionUrlToFile,
  loadSessionUrlFromFile,
} = require('../../shared/session');
const { randomDelay, sleep } = require('../../shared/utils');
const {brandNames} = require('./supplier_names');

// Keep the original constants for backward compatibility
const SCRAPER_NAME = 'AUTOTOTAL';
const STATE_DIR = path.resolve(__dirname, 'state');
const COOKIES_PATH = path.join(STATE_DIR, 'cookies.json');
const SESSION_URL_PATH = path.join(STATE_DIR, 'session-url.txt');
const PROFILE_PATH = path.join(STATE_DIR, 'profile');

const LOGIN_URL = 'https://www3.eoriginal.ro/login';
const DEFAULT_TEST_PAGE = 'https://www3.eoriginal.ro/#/';
const LOGGED_IN_SELECTOR = '#userMenuBtn';
const USERNAME_SELECTOR = 'input[name="username"]';
const PASSWORD_SELECTOR = 'input[name="password"]';
const LOGIN_BUTTON_SELECTOR = 'button[type="submit"].btn-primary';
const DEVICE_NAME_SELECTOR = 'input[name="deviceName"]';
const DEVICE_SAVE_BUTTON_SELECTOR = 'button[type="submit"].btn-primary';

const SEARCH_INPUT_SELECTOR = '#searchBoxIbcs';
const SEARCH_BUTTON_SELECTOR = 'button.src_btn';
const PRICE_SELECTOR = '.price_final > span';
const RESULTS_TABLE_SELECTOR = '.art_wrapper';
const PRICE_REQUEST_BUTTON_SELECTOR = 'button.btn-lightx.bold';

async function runAutototalScraper(productCode) {
  console.log(`🚀 Starting ${SCRAPER_NAME} scraper for code: ${productCode}`);
  let browser;
  try {
    const { browser: launchedBrowser, page } = await launchBrowser({ userDataDir: PROFILE_PATH });
    browser = launchedBrowser;

    let testPage = loadSessionUrlFromFile(SESSION_URL_PATH) || DEFAULT_TEST_PAGE;
    await loadCookiesFromFile(page, COOKIES_PATH);
    await page.goto(testPage, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await sleep(randomDelay(500, 1000));

    try {
      await page.waitForSelector(LOGGED_IN_SELECTOR, { timeout: 1000 });
    } catch {
      console.log('🔐 Not logged in or session expired. Performing login...');

      const isOnLoginPage = await page.$(USERNAME_SELECTOR) !== null;

      if (!isOnLoginPage) {
        console.log(`Navigating to login page: ${LOGIN_URL}`);
        await page.goto(LOGIN_URL, { waitUntil: 'domcontentloaded', timeout: 6000 });
      } else {
        console.log('🔄 Already on the login page.');
      }

      await page.waitForSelector(USERNAME_SELECTOR);
      await page.type(USERNAME_SELECTOR, process.env.AUTOTOTAL_USER, { delay: randomDelay() });
      await sleep(randomDelay(300, 600));
      await page.type(PASSWORD_SELECTOR, process.env.AUTOTOTAL_PASSWORD, { delay: randomDelay() });
      await sleep(randomDelay(300, 600));
      await page.click(LOGIN_BUTTON_SELECTOR);
      await page.waitForNavigation({ waitUntil: 'networkidle2' });
      if (page.url().includes('/device/check-device')) {
        await page.type(DEVICE_NAME_SELECTOR, process.env.AUTOTOTAL_DEVICE, { delay: randomDelay() });
        await sleep(randomDelay(300, 600));
        await page.click(DEVICE_SAVE_BUTTON_SELECTOR);
        await page.waitForSelector(LOGGED_IN_SELECTOR);
      } else {
        await page.waitForSelector(LOGGED_IN_SELECTOR);
      }
      await saveCookiesToFile(page, COOKIES_PATH);
      saveSessionUrlToFile(page.url(), SESSION_URL_PATH);
    }

    await page.waitForSelector(SEARCH_INPUT_SELECTOR);
    await page.evaluate(sel => document.querySelector(sel).value = '', SEARCH_INPUT_SELECTOR);
    await page.type(SEARCH_INPUT_SELECTOR, productCode, { delay: randomDelay() });
    await sleep(randomDelay(300, 600));
    await page.click(SEARCH_BUTTON_SELECTOR);



    // Wait for results table with a longer timeout
    console.log('⏳ Waiting for search results to load...');
    try {
      await page.waitForSelector(RESULTS_TABLE_SELECTOR, { visible: true, timeout: 20000 });
      console.log('✅ Search results loaded successfully');
    } catch (error) {
      console.log('⚠️ Timeout waiting for search results, will try to continue anyway');
    }

    // Check for pagination with a longer timeout
    console.log('🔍 Checking for pagination...');
    try {
      await page.waitForSelector('ul.pagination', { visible: true, timeout: 5000 });
      console.log('✅ Pagination found');
    } catch (e) {
      console.log('ℹ️ No pagination found - this might be a single page of results');
    }
    const hasPagination = await page.evaluate(() => {
      const pag = document.querySelector('ul.pagination');
      return pag && getComputedStyle(pag).display !== 'none';
    });

    const results = [];
    const visitedPages = new Set();
    const MAX_PAGES_TO_VISIT = 50; // Safety limit to prevent infinite loops

    if (!hasPagination && visitedPages.size === 0) {
      console.log('🟢 Only one page — scraping directly');
    }


    if (!hasPagination && visitedPages.size === 0) {
      // Run just once
      await sleep(randomDelay(300, 600));
      await page.waitForSelector(RESULTS_TABLE_SELECTOR, { visible: true, timeout: 10000 });
      console.log('⏳ Waiting for all products to have price or request button...');
      try {
        await page.waitForFunction(() => {
          const productCards = document.querySelectorAll('.art_wrapper .article-card');
          return Array.from(productCards).every(card => {
            const hasPrice = card.querySelector('.price_final span');
            const hasButton = card.querySelector('button.btn-lightx.bold');
            return hasPrice || hasButton;
          });
        }, { timeout: 30000 });
        console.log('✅ All products have price info. Scraping...');
      } catch {
        console.warn('⚠️ Not all products had price info after timeout. Continuing anyway...');
      }
      const pageData = await page.evaluate((brandNames) => {
        // Define the parseTitleWithRegex function in the browser context
        function parseTitleWithRegex(titleText, brandNames) {
          // 1. Escape & sort brands by length
          const escaped = brandNames
            .map(b => b.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))
            .sort((a, b) => b.length - a.length);

          // 2. Build: ^(BrandA|Brand B|...)\s+(.+)$
          const re = new RegExp(`^(${escaped.join('|')})\\s+(.+)$`, 'i');
          const m = titleText.match(re);

          if (m) {
            return {
              brand: m[1],
              mainCode: m[2].trim(),
            };
          }
          // Fallback as before
          const [first, ...rest] = titleText.split(' ');
          return { brand: first, mainCode: rest.join(' ') };
        }

        const items = [];
        const productCards = document.querySelectorAll('.art_wrapper .article-card');

        productCards.forEach(card => {
          const titleText = card.querySelector('h6.card-title span')?.textContent.trim() || '';
          const { brand, productCode } = parseTitleWithRegex(titleText, brandNames);

          const name = card.querySelector('.card-descr span')?.textContent.trim() || 'N/A';

          const priceText = card.querySelector('.price_final span')?.textContent.trim();
          let adjustedPrice = null;
          let price = null;

          if (priceText) {
            // Clean and parse the price, then apply the formula
            const numeric = parseFloat(priceText.replace(/[^\d,]/g, '').replace(',', '.'));
            if (!isNaN(numeric)) {
              adjustedPrice = Math.round(numeric * 1.19 * 1.2) / 100;
              price = adjustedPrice; // Use this instead of the raw string
            }
          } else {
            const btn = card.querySelector('button.btn-lightx.bold');
            if (btn) price = 'Available via request';
          }

          const availability = card.querySelector('.stoc_texts')?.textContent.trim() || null;

          // ✅ New logic for extracting `exchangeValue`
          let exchangeValue = null;
          const piesaSchimbBlock = card.querySelector('small.piesas-wrapper');
          if (piesaSchimbBlock && piesaSchimbBlock.innerText.includes('Piesa schimb')) {
            const spans = piesaSchimbBlock.querySelectorAll('span');
            if (spans.length > 1) {
              const rawValue = spans[1].textContent.trim();
              const parsed = parseFloat(rawValue.replace(/[^\d,]/g, '').replace(',', '.'));
              if (!isNaN(parsed)) {
                exchangeValue = parsed / 100;
              }
            }
          }

          if (productCode) {
            items.push({
              name,
              brand: brand || 'N/A',
              productCode,
              internalCode: null,
              delivery: null,
              retailPrice: price || 'N/A',
              exchangeValue,
              availability: availability,
              returnable: true,
              provider: 'AUTOTOTAL'
            });
          }

        });
        return items;
      }, brandNames);


      results.push(...pageData);
    } else {
      let pageVisitCount = 0;
      while (pageVisitCount < MAX_PAGES_TO_VISIT) {
        pageVisitCount++;
        // Wait for pagination to be visible
        await page.waitForSelector('ul.pagination', { visible: true, timeout: 10000 });

        // Ensure pagination is fully visible by scrolling to it
        await page.evaluate(() => {
          const paginationElement = document.querySelector('ul.pagination');
          if (paginationElement) {
            // Scroll to the pagination with some space above it - using instant scroll instead of smooth
            paginationElement.scrollIntoView({ block: 'center' });

            // Add a subtle highlight without animation
            paginationElement.style.outline = '2px solid rgba(0, 123, 255, 0.5)';
          }
        });

        // Minimal delay after scrolling - just enough for the DOM to update
        await sleep(100);
        const activePage = await page.evaluate(() => {
          const active = document.querySelector('ul.pagination li.page-item.active');
          return active ? active.textContent.trim() : null;
        });

        // Check if we're back on page 1 after having visited other pages
        if (activePage === '1' && visitedPages.size > 0 && visitedPages.has('1')) {
          console.log('⚠️ Detected navigation back to page 1 after visiting other pages');
          console.log('🔄 This might indicate we\'ve completed a full cycle through all pages');

          // Check if we've visited a significant number of pages before deciding to stop
          const highestPageVisited = Math.max(...Array.from(visitedPages).map(p => parseInt(p) || 0));
          console.log(`ℹ️ Highest page number visited: ${highestPageVisited}`);

          if (highestPageVisited > 2) {
            console.log('✅ Assuming we\'ve visited all pages since we\'ve returned to page 1');
            break;
          } else {
            console.log('⚠️ Only visited a few pages before returning to page 1, might be a navigation issue');
            // Continue and let the retry logic handle it
          }
        }

        // Skip pages we've already visited
        if (!activePage || visitedPages.has(activePage)) {
          console.log(`🔄 Skipping already visited page: ${activePage}`);
          break;
        }

        console.log(`📄 Processing page ${activePage}`);
        visitedPages.add(activePage);

        await page.waitForSelector(RESULTS_TABLE_SELECTOR, { visible: true });
        const prevHtml = await page.$eval(RESULTS_TABLE_SELECTOR, el => el.innerHTML);

        console.log('⏳ Waiting for all products to have price or request button...');
        try {
          await page.waitForFunction(() => {
            const productCards = document.querySelectorAll('.art_wrapper .article-card');
            return Array.from(productCards).every(card => {
              const hasPrice = card.querySelector('.price_final span');
              const hasButton = card.querySelector('button.btn-lightx.bold');
              return hasPrice || hasButton;
            });
          }, { timeout: 30000 });
          console.log('✅ All products have price info. Scraping...');
        } catch {
          console.warn('⚠️ Not all products had price info after timeout. Continuing anyway...');
        }

        const pageData = await page.evaluate((brandNames) => {
          // Define the parseTitleWithRegex function in the browser context
          function parseTitleWithRegex(titleText, brandNames) {
            // 1. Escape & sort brands by length
            const escaped = brandNames
              .map(b => b.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))
              .sort((a, b) => b.length - a.length);

            // 2. Build: ^(BrandA|Brand B|...)\s+(.+)$
            const re = new RegExp(`^(${escaped.join('|')})\\s+(.+)$`, 'i');
            const m = titleText.match(re);

            if (m) {
              return {
                brand: m[1],
                productCode: m[2].trim(),
              };
            }
            // Fallback as before
            const [first, ...rest] = titleText.split(' ');
            return { brand: first, productCode: rest.join(' ') };
          }

          const items = [];
          const productCards = document.querySelectorAll('.art_wrapper .article-card');

          productCards.forEach(card => {
            const titleText = card.querySelector('h6.card-title span')?.textContent.trim() || '';
            const { brand, productCode } = parseTitleWithRegex(titleText, brandNames);


            const name = card.querySelector('.card-descr span')?.textContent.trim() || 'N/A';

            const priceText = card.querySelector('.price_final span')?.textContent.trim();
            let adjustedPrice = null;
            let price = null;

            if (priceText) {
              // Clean and parse the price, then apply the formula
              const numeric = parseFloat(priceText.replace(/[^\d,]/g, '').replace(',', '.'));
              if (!isNaN(numeric)) {
                adjustedPrice = Math.round(numeric * 1.19 * 1.2) / 100;
                price = adjustedPrice; // Use this instead of the raw string
              }
            } else {
              const btn = card.querySelector('button.btn-lightx.bold');
              if (btn) price = 'Available via request';
            }

            const availability = card.querySelector('.stoc_texts')?.textContent.trim() || null;

            // ✅ New logic for extracting `exchangeValue`
            let exchangeValue = null;
            const piesaSchimbBlock = card.querySelector('small.piesas-wrapper');
            if (piesaSchimbBlock && piesaSchimbBlock.innerText.includes('Piesa schimb')) {
              const spans = piesaSchimbBlock.querySelectorAll('span');
              if (spans.length > 1) {
                const rawValue = spans[1].textContent.trim();
                const parsed = parseFloat(rawValue.replace(/[^\d,]/g, '').replace(',', '.'));
                if (!isNaN(parsed)) {
                  exchangeValue = parsed / 100;
                }
              }
            }


             if(productCode){
              items.push({
                name,
                brand: brand || 'N/A',
                productCode,
                internalCode: null,
                delivery: null,
                retailPrice: price || 'N/A',
                exchangeValue,
                availability: availability,
                returnable: true,
                provider: 'AUTOTOTAL'
              });
             }

          });
          return items;
        }, brandNames);

        results.push(...pageData);

        // Ensure pagination links are clickable with a more efficient approach
        await page.evaluate(() => {
          const pagination = document.querySelector('ul.pagination');
          if (pagination) {
            // Direct style application without forced reflow
            pagination.style.pointerEvents = 'auto';

            // Make links clickable with a single operation
            const links = pagination.querySelectorAll('a.page-link');
            for (const link of links) {
              link.style.pointerEvents = 'auto';
              link.style.cursor = 'pointer';
            }
          }
        });

        // Get all page links that aren't the active page, first, or last items
        const pageLinks = await page.$$('ul.pagination li.page-item:not(.active):not(:first-child):not(:last-child) a.page-link');

        // Log the number of available page links
        console.log(`🔢 Found ${pageLinks.length} available page links`);

        let foundNext = false;

        // Get the next page link that hasn't been visited
        let nextLinkInfo = null;
        for (const link of pageLinks) {
          // Simplified visibility check - just check if element is attached to DOM
          const isAttached = await page.evaluate(el => {
            return !!el.isConnected;
          }, link);

          if (!isAttached) {
            console.log('⚠️ Skipping detached page link');
            continue;
          }

          const text = await page.evaluate(el => el.textContent.trim(), link);
          console.log(`🔍 Checking page link: ${text}`);

          if (!visitedPages.has(text)) {
            nextLinkInfo = { link, text };
            console.log(`✅ Found unvisited page link: ${text}`);
            break;
          } else {
            console.log(`🔄 Already visited page: ${text}`);
          }
        }

        if (nextLinkInfo) {
          const { link, text } = nextLinkInfo;
          console.log(`🔄 Attempting to navigate to page ${text}...`);

          // Retry logic for clicking the page link
          const maxRetries = 3;
          let retryCount = 0;
          let navigationSuccessful = false;

          while (retryCount < maxRetries && !navigationSuccessful) {
            try {
              if (retryCount > 0) {
                console.log(`🔄 Retry attempt ${retryCount} for page ${text}...`);
                await sleep(randomDelay(1000, 2000)); // Longer delay between retries
              }

              // Fast scroll to make the link visible
              await page.evaluate(linkElement => {
                // Direct scroll without animation
                linkElement.scrollIntoView({ block: 'nearest' });
              }, link);

              // Minimal delay - just enough for the browser to register the new scroll position
              await sleep(50);

              // Try multiple click methods to ensure the click is registered
              try {
                // Method 1: Standard Puppeteer click
                await link.click();
                console.log('✅ Standard click performed');
              } catch (clickError) {
                console.log(`⚠️ Standard click failed: ${clickError.message}`);

                // Method 2: Try JavaScript click as fallback
                try {
                  await page.evaluate(el => {
                    // Simulate a more natural click with mouse events
                    const mouseDown = new MouseEvent('mousedown', {
                      bubbles: true,
                      cancelable: true,
                      view: window
                    });

                    const mouseUp = new MouseEvent('mouseup', {
                      bubbles: true,
                      cancelable: true,
                      view: window
                    });

                    const click = new MouseEvent('click', {
                      bubbles: true,
                      cancelable: true,
                      view: window
                    });

                    // Dispatch the events in sequence
                    el.dispatchEvent(mouseDown);
                    el.dispatchEvent(mouseUp);
                    el.dispatchEvent(click);

                    console.log('JavaScript click events dispatched');
                  }, link);
                  console.log('✅ JavaScript click performed as fallback');
                } catch (jsClickError) {
                  console.log(`⚠️ JavaScript click also failed: ${jsClickError.message}`);
                  throw new Error('All click methods failed');
                }
              }

              // Wait for content to change with timeout
              await page.waitForFunction(
                (sel, prev) => document.querySelector(sel)?.innerHTML !== prev,
                { timeout: 5000 },
                RESULTS_TABLE_SELECTOR,
                prevHtml
              );

              // Verify that we actually navigated to the expected page
              const newActivePage = await page.evaluate(() => {
                const active = document.querySelector('ul.pagination li.page-item.active');
                return active ? active.textContent.trim() : null;
              });

              if (newActivePage === text) {
                console.log(`✅ Successfully navigated to page ${text}`);
                navigationSuccessful = true;
              } else {
                console.log(`⚠️ Navigation issue: Expected page ${text}, but active page is ${newActivePage}`);
                retryCount++;
              }
            } catch (error) {
              console.log(`⚠️ Navigation error on attempt ${retryCount + 1}: ${error.message}`);
              retryCount++;
            }
          }

          if (navigationSuccessful) {
            await sleep(randomDelay(1000, 1500));
            foundNext = true;
          } else {
            console.log(`❌ Failed to navigate to page ${text} after ${maxRetries} attempts`);
          }
        }

        // If we couldn't find or navigate to a numbered page, try using the "Next" button as a fallback
        if (!foundNext) {
          console.log('🔄 Trying to use the "Next" button as a fallback...');
          try {
            // Find the "Next" button (last item in pagination)
            const nextButton = await page.$('ul.pagination li.page-item:last-child a.page-link');
            if (nextButton) {
              // Check if we're on the last page by examining the "Next" button
              const isNextDisabled = await page.evaluate(btn => {
                const parentLi = btn.closest('li.page-item');
                return parentLi.classList.contains('disabled');
              }, nextButton);

              if (!isNextDisabled) {
                console.log('🔄 Attempting to use the "Next" button...');
                const prevHtml = await page.$eval(RESULTS_TABLE_SELECTOR, el => el.innerHTML);

                // Retry logic for the Next button
                const maxRetries = 3;
                let retryCount = 0;
                let navigationSuccessful = false;

                while (retryCount < maxRetries && !navigationSuccessful) {
                  try {
                    if (retryCount > 0) {
                      console.log(`🔄 Retry attempt ${retryCount} for Next button...`);
                      await sleep(randomDelay(1000, 2000));
                    }

                    // Get the current active page before clicking
                    const beforePage = await page.evaluate(() => {
                      const active = document.querySelector('ul.pagination li.page-item.active');
                      return active ? active.textContent.trim() : null;
                    });

                    // Fast scroll to make the Next button visible
                    await page.evaluate(buttonElement => {
                      // Direct scroll without animation
                      buttonElement.scrollIntoView({ block: 'nearest' });
                    }, nextButton);

                    // Minimal delay - just enough for the browser to register the new scroll position
                    await sleep(50);

                    // Try multiple click methods to ensure the Next button click is registered
                    try {
                      // Method 1: Standard Puppeteer click
                      await nextButton.click();
                      console.log('✅ Standard Next button click performed');
                    } catch (clickError) {
                      console.log(`⚠️ Standard Next button click failed: ${clickError.message}`);

                      // Method 2: Try JavaScript click as fallback
                      try {
                        await page.evaluate(el => {
                          // Simulate a more natural click with mouse events
                          const mouseDown = new MouseEvent('mousedown', {
                            bubbles: true,
                            cancelable: true,
                            view: window
                          });

                          const mouseUp = new MouseEvent('mouseup', {
                            bubbles: true,
                            cancelable: true,
                            view: window
                          });

                          const click = new MouseEvent('click', {
                            bubbles: true,
                            cancelable: true,
                            view: window
                          });

                          // Dispatch the events in sequence
                          el.dispatchEvent(mouseDown);
                          el.dispatchEvent(mouseUp);
                          el.dispatchEvent(click);

                          console.log('JavaScript Next button click events dispatched');
                        }, nextButton);
                        console.log('✅ JavaScript Next button click performed as fallback');
                      } catch (jsClickError) {
                        console.log(`⚠️ JavaScript Next button click also failed: ${jsClickError.message}`);
                        throw new Error('All Next button click methods failed');
                      }
                    }

                    // Wait for content to change
                    await page.waitForFunction(
                      (sel, prev) => document.querySelector(sel)?.innerHTML !== prev,
                      { timeout: 5000 },
                      RESULTS_TABLE_SELECTOR,
                      prevHtml
                    );

                    // Verify we moved to a different page
                    const afterPage = await page.evaluate(() => {
                      const active = document.querySelector('ul.pagination li.page-item.active');
                      return active ? active.textContent.trim() : null;
                    });

                    if (afterPage && afterPage !== beforePage) {
                      console.log(`✅ Successfully navigated to page ${afterPage} using Next button`);
                      visitedPages.add(afterPage); // Mark this page as visited
                      navigationSuccessful = true;
                      foundNext = true;
                    } else {
                      console.log(`⚠️ Next button click didn't change the active page (before: ${beforePage}, after: ${afterPage})`);
                      retryCount++;
                    }
                  } catch (error) {
                    console.log(`⚠️ Next button navigation error on attempt ${retryCount + 1}: ${error.message}`);
                    retryCount++;
                  }
                }

                if (navigationSuccessful) {
                  await sleep(randomDelay(1000, 1500));
                } else {
                  console.log('❌ Failed to navigate using Next button after multiple attempts');
                  break;
                }
              } else {
                console.log('🛑 Next button is disabled - likely on the last page');
                break;
              }
            } else {
              console.log('❌ Could not find Next button');
              break;
            }
          } catch (error) {
            console.log(`❌ Error while trying to use Next button: ${error.message}`);
            break;
          }
        }

        // If we still couldn't navigate to the next page after all attempts, break the loop
        if (!foundNext) break;
      }

      // Check if we hit the maximum page limit
      if (pageVisitCount >= MAX_PAGES_TO_VISIT) {
        console.log(`⚠️ Reached maximum page visit limit (${MAX_PAGES_TO_VISIT}). Stopping pagination to prevent infinite loop.`);
      }
    }
    console.log(`📊 Scraped ${results.length} products.`);
    console.log(JSON.stringify(results, null, 2)); // Output results

    //await browser.close();
    console.log(`✅ ${SCRAPER_NAME} scraper finished successfully.`);
    return results;

  } catch (err) {
    console.error(`❌ Error in ${SCRAPER_NAME}:`, err);
    if (browser) await browser.close();
    return null;
  }
}

module.exports = {
  runAutototalScraper,
  // Export the modular version as well
  runAutototalScraperModular,
};
