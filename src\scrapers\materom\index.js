const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });

const { launchBrowser } = require('../../shared/browser');
const {
  saveCookiesToFile,
  loadCookiesFromFile,
  saveSessionUrlToFile,
  loadSessionUrlFromFile,
} = require('../../shared/session');
const { randomDelay, sleep } = require('../../shared/utils');

const SCRAPER_NAME = 'MATEROM';
const STATE_DIR = path.resolve(__dirname, 'state');
const COOKIES_PATH = path.join(STATE_DIR, 'cookies.json');
const SESSION_URL_PATH = path.join(STATE_DIR, 'session-url.txt');
const PROFILE_PATH = path.join(STATE_DIR, 'profile');

const LOGIN_URL = 'https://shop.materom.ro/login';
const DEFAULT_TEST_PAGE = 'https://shop.materom.ro/home';

const LOGGED_IN_SELECTOR = 'button[id^="headlessui-menu-button"] div:first-child'; // Shows the username when logged in
const USERNAME_SELECTOR = 'input#username';
const PASSWORD_SELECTOR = 'input#password';
const LOGIN_BUTTON_SELECTOR = 'button[type="submit"].bg-materom-blue';

const SEARCH_INPUT_SELECTOR = 'input[placeholder*="Caută după cod"]';

const RESULTS_SECTION_SELECTOR = 'section.grow.w-full';
const RESULT_ITEMS_SELECTOR = 'ul.divide-y > li';

async function runMateromScraper(productCode) {
  console.log(`🚀 Starting ${SCRAPER_NAME} scraper for code: ${productCode}`);
  let browser;
  try {
    const { browser: launchedBrowser, page } = await launchBrowser({ userDataDir: PROFILE_PATH });
    browser = launchedBrowser;

    let testPage = loadSessionUrlFromFile(SESSION_URL_PATH) || DEFAULT_TEST_PAGE;
    await loadCookiesFromFile(page, COOKIES_PATH);
    await page.goto(testPage, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await sleep(randomDelay(500, 1000));

    try {
      await page.waitForSelector(LOGGED_IN_SELECTOR, { timeout: 1000 });
      console.log('✅ Already logged in. Continuing...');
    } catch {
      console.log('🔐 Not logged in or session expired. Performing login...');

      const isOnLoginPage = await page.$(USERNAME_SELECTOR) !== null;

      if (!isOnLoginPage) {
        console.log(`Navigating to login page: ${LOGIN_URL}`);
        await page.goto(LOGIN_URL, { waitUntil: 'domcontentloaded', timeout: 6000 });
      } else {
        console.log('🔄 Already on the login page.');
      }

      await page.waitForSelector(USERNAME_SELECTOR);
      await page.type(USERNAME_SELECTOR, process.env.MATEROM_USER, { delay: randomDelay() });
      await sleep(randomDelay(300, 600));
      await page.type(PASSWORD_SELECTOR, process.env.MATEROM_PASSWORD, { delay: randomDelay() });
      await sleep(randomDelay(300, 600));
      await page.click(LOGIN_BUTTON_SELECTOR);
      await page.waitForNavigation({ waitUntil: 'networkidle2' });
      await saveCookiesToFile(page, COOKIES_PATH);
      saveSessionUrlToFile(page.url(), SESSION_URL_PATH);
    }

    console.log(`🔎 Searching for product code: ${productCode}`);

    await page.waitForSelector(SEARCH_INPUT_SELECTOR, { timeout: 5000 });
    await page.evaluate(sel => document.querySelector(sel).value = '', SEARCH_INPUT_SELECTOR); // clear any existing value
    await page.type(SEARCH_INPUT_SELECTOR, productCode, { delay: randomDelay(50, 100) });
    await sleep(randomDelay(300, 600));

    // Click the button that contains the SVG icon (has to go up to button from SVG)
    // console.log('🔍 Looking for the search button inside .flex.flex-shrink-0...');
    // const searchButton = await page.$('div.flex.flex-shrink-0 > button:nth-of-type(2)');
    // if (!searchButton) {
    //   throw new Error('❌ Could not find the search button.');
    // }
    // console.log('🖱️ Clicking the search button...');
    // await searchButton.click();
    // console.log('🚀 Search submitted. Waiting for results...');
    // await sleep(2000);
    // await page.waitForSelector(RESULTS_SECTION_SELECTOR, { timeout: 15000 });
    // console.log('📦 Results section loaded.');


    // ensure the input has focus
    await page.focus(SEARCH_INPUT_SELECTOR);

    // press Enter to submit
    console.log('⏎ Pressing Enter to submit search…');
    await page.keyboard.press('Enter');

    // wait for results as before
    console.log('🚀 Search submitted. Waiting for results...');
    await sleep(2000);
    await page.waitForSelector(RESULTS_SECTION_SELECTOR, { timeout: 15000 });
    console.log('📦 Results section loaded.');

    const maxWaitTime = 15000;
    const pollInterval = 500;
    const minExpectedResults = 2; // adjust this threshold if needed

    let itemsCount = 0;
    const start = Date.now();

    while (Date.now() - start < maxWaitTime) {
      itemsCount = await page.evaluate((sel) => {
        return document.querySelectorAll(sel).length;
      }, RESULT_ITEMS_SELECTOR);

      if (itemsCount >= minExpectedResults) {
        break;
      }

      await sleep(pollInterval);
    }

    if (itemsCount < minExpectedResults) {
      console.warn(`⚠️ Only ${itemsCount} result item(s) detected after timeout.`);
    } else {
      console.log(`🔍 Detected ${itemsCount} product items.`);
    }


    console.log('⏳ Waiting until all product items have finished loading delivery data...');

    const maxWaitForNoSpinner = 25000;
    const spinnerPollInterval = 400;

    let allSpinnersGone = false;
    const spinnerCheckStart = Date.now();

    while (Date.now() - spinnerCheckStart < maxWaitForNoSpinner) {
      const spinnersRemaining = await page.evaluate((liSelector) => {
        return Array.from(document.querySelectorAll(liSelector)).filter(li => {
          return li.querySelector('div.flex.items-left.text-gray-400 svg.animate-spin');
        }).length;
      }, RESULT_ITEMS_SELECTOR);

      //console.log(`🌀 Still waiting... ${spinnersRemaining} item(s) with loading spinners.`);

      if (spinnersRemaining === 0) {
        allSpinnersGone = true;
        break;
      }

      await sleep(spinnerPollInterval);
    }

    if (allSpinnersGone) {
      console.log('✅ All product items finished loading delivery/prices.');
    } else {
      console.warn('⚠️ Some products still had loading indicators. Proceeding anyway...');
    }


    const results = await page.evaluate((liSelector) => {
      const items = [];
      const listItems = document.querySelectorAll(liSelector);

      listItems.forEach(li => {
        // Skip items that still have the loading spinner
        if (li.querySelector('div.flex.items-left.text-gray-400 svg.animate-spin')) return;

        // Get price
        const priceEl = li.querySelector('div.text-sm.whitespace-nowrap.tabular-nums');
        const priceText = priceEl?.textContent.trim();
        if (!priceText) return; // Skip if no price

        // Convert price to numeric format
        let retailPrice = null;
        // Extract numeric part (e.g., "2.828,72 RON" -> "2.828,72")
        const priceMatch = priceText.match(/([\d.,]+)/);
        if (priceMatch) {
          // Handle Romanian number format (2.828,72)
          // First, remove all dots (thousands separators)
          // Then, replace comma with dot (decimal separator)
          const cleanedPrice = priceMatch[1].replace(/\./g, '').replace(',', '.');
          const numericPrice = parseFloat(cleanedPrice);
          if (!isNaN(numericPrice)) {
            retailPrice = numericPrice;
          } else {
            retailPrice = priceText;
          }
        } else {
          retailPrice = priceText;
        }

        // Get name
        const name = li.querySelector('div.text-left.text-xs.font-semibold')?.textContent.trim() || 'N/A';

        // Get brand and code
        const brand = li.querySelector('div.flex.flex-col.sm\\:flex-row button')?.textContent.trim() || 'N/A';
        const code = li.querySelector('div.flex.flex-col.sm\\:flex-row .flex.items-center > div')?.textContent.trim() || 'N/A';

        const deliveryContainer = li.querySelector('div.flex.items-left.mt-5'); // Or a more specific parent if needed

        // Now find the specific div with the text inside that container
        const deliveryEl = deliveryContainer?.querySelector('div.ml-1'); // Target the div with class ml-1
        const delivery = deliveryEl?.textContent.trim() || 'N/A'; // Get its text content

        // Extract exchange value if available
        let exchangeValue = null;

        // Look for the blue info box that contains exchange value information
        const exchangeInfoBox = li.querySelector('div.rounded-md.bg-blue-50');
        if (exchangeInfoBox) {
          // Check if the info box contains text about exchange value
          const infoText = exchangeInfoBox.textContent.trim();
          if (infoText.includes('piesă veche la schimb') || infoText.includes('se facturează cu')) {


            // Find the span with the exchange value amount
            const exchangeValueSpan = exchangeInfoBox.querySelector('span.text-red-700, span.text-gray-800, span[title*="Preț de achiziție"]');
            if (exchangeValueSpan) {
              // Get the raw text value (e.g., "2.563,64 RON")
              const rawValue = exchangeValueSpan.textContent.trim();

              // Extract just the numeric part and handle formatting
              // Romanian format uses dot as thousand separator and comma as decimal separator
              const valueMatch = rawValue.match(/([0-9.,]+)/);
              if (valueMatch && valueMatch[1]) {
                // Convert to numeric format
                // First, remove all dots (thousands separators)
                // Then, replace comma with dot (decimal separator)
                const cleanedValue = valueMatch[1].replace(/\./g, '').replace(',', '.');
                const numericValue = parseFloat(cleanedValue);
                if (!isNaN(numericValue)) {
                  exchangeValue = numericValue;
                } else {
                  exchangeValue = rawValue;
                }
              } else {
                exchangeValue = rawValue;
              }
            } else {
              // Try to extract the value from the text directly if no span is found
              const valueMatch = infoText.match(/schimb\s+[^0-9]*([0-9.,]+)/);
              if (valueMatch && valueMatch[1]) {
                // Convert to numeric format
                // First, remove all dots (thousands separators)
                // Then, replace comma with dot (decimal separator)
                const cleanedValue = valueMatch[1].replace(/\./g, '').replace(',', '.');
                const numericValue = parseFloat(cleanedValue);
                if (!isNaN(numericValue)) {
                  exchangeValue = numericValue;
                } else {
                  exchangeValue = valueMatch[1];
                }
              }
            }
          }
        }


        let returnable = true;
        // Check for the specific non-returnable indicator
        // This selector covers both light and dark mode versions of the element
        const nonReturnableElement = li.querySelector('div.rounded-md.font-semibold[class*="bg-materom-blue"], div.rounded-md.font-semibold.bg-gray-300');

        if (nonReturnableElement) {
          const elementText = nonReturnableElement.textContent.toLowerCase().trim();
          if (elementText.includes('nu poate fi returnat')) {
            // If the element explicitly states the product cannot be returned, override any previous setting
            returnable = false;
          }
        }


        items.push({
          name,
          brand,
          code,
          internalCode: null,
          delivery_options: delivery,
          price: retailPrice,
          exchangeValue,
          returnable,
          provider: 'MATEROM'
        });
      });
      return items;
    }, RESULT_ITEMS_SELECTOR);

    console.log(`📊 Scraped ${results.length} products.`);
    console.log(JSON.stringify(results, null, 2)); // Output results

    await browser.close();
    console.log(`✅ ${SCRAPER_NAME} scraper finished successfully.`);
    return results;

  } catch (err) {
    console.error(`❌ Error in ${SCRAPER_NAME}:`, err);
    if (browser) await browser.close();
    return null;
  }
}

module.exports = {
  runMateromScraper,
};

