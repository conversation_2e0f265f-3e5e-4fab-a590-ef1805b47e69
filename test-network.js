/**
 * Network Throttling Test for Supplier Scrapers
 *
 * This script tests scrapers under different network conditions
 * to evaluate their performance and resilience to poor network connectivity.
 *
 * It compares results across different network conditions for discrepancies
 * and generates a comprehensive console report with performance metrics.
 *
 * Usage: node test-network.js <provider> <product-code>
 * Example: node test-network.js autopartner **********
 */

// Import all scrapers
const { runAutonetScraper } = require('./src/scrapers/autonet/index');
const { runAutototalScraperModular } = require('./src/scrapers/autototal/index');
const { runIntercarsScraper } = require('./src/scrapers/intercars/index');
const { runAutopartnerScraper } = require('./src/scrapers/autopartner/index');
const { runMateromScraper } = require('./src/scrapers/materom/index');
const { runElitScraper } = require('./src/scrapers/elit/index');
const { runBardiScraper } = require('./src/scrapers/bardi/index');

const NETWORK_PRESETS = {
  FAST: {
    name: 'Fast 4G',
    config: {
      downloadSpeed: 4 * 1024 * 1024, // 4 MB/s
      uploadSpeed: 2 * 1024 * 1024,   // 2 MB/s
      latency: 20                     // 20ms
    }
  },
  AVERAGE: {
    name: 'Average 3G',
    config: {
      downloadSpeed: 1 * 1024 * 1024, // 1 MB/s
      uploadSpeed: 500 * 1024,        // 500 KB/s
      latency: 200                    // 200ms
    }
  },
  SLOW: {
    name: 'Slow Connection',
    config: {
      downloadSpeed: 100 * 1024,      // 100 KB/s
      uploadSpeed: 50 * 1024,         // 50 KB/s
      latency: 800                    // 800ms
    }
  },
  TERRIBLE: {
    name: 'Terrible Connection',
    config: {
      downloadSpeed: 30 * 1024,       // 30 KB/s
      uploadSpeed: 10 * 1024,         // 10 KB/s
      latency: 2000                   // 2 seconds latency
    }
  },
  PACKET_LOSS: {
    name: 'Packet Loss (10%)',
    config: {
      downloadSpeed: 200 * 1024,      // 200 KB/s
      uploadSpeed: 100 * 1024,        // 100 KB/s
      latency: 500,                   // 500ms
      packetLoss: 0.1                 // 10% packet loss
    }
  },
  NIGHTMARE: {
    name: 'Nightmare Connection',
    config: {
      downloadSpeed: 20 * 1024,        // 5 KB/s (dial-up modem speed)
      uploadSpeed: 10 * 1024,          // 2 KB/s
      latency: 3000,                  // 3 seconds latency
      packetLoss: 0.2,                // 20% packet loss
      jitter: 500                     // 500ms jitter (variable latency)
    }
  }
};


// Map provider names to their scraper functions
const SCRAPER_FUNCTIONS = {
  'autonet': runAutonetScraper,
  'autototal': runAutototalScraperModular,
  'intercars': runIntercarsScraper,
  'autopartner': runAutopartnerScraper,
  'materom': runMateromScraper,
  'elit': runElitScraper,
  'bardi': runBardiScraper
};

/**
 * Run a scraper with specific network conditions
 * @param {string} provider - Provider name
 * @param {string} productCode - Product code to search for
 * @param {object} networkPreset - Network preset to apply
 * @returns {Promise<object>} - Test results
 */
async function runScraperWithNetworkConditions(provider, productCode, networkPreset) {
  console.log(`\n========================================`);
  console.log(`🧪 TESTING ${provider.toUpperCase()} WITH ${networkPreset.name.toUpperCase()} CONDITIONS`);

  // Build network conditions string
  let networkConditions = `   ↓ ${networkPreset.config.downloadSpeed/1024} KB/s, ↑ ${networkPreset.config.uploadSpeed/1024} KB/s, ${networkPreset.config.latency}ms latency`;

  // Add packet loss if present
  if (networkPreset.config.packetLoss) {
    networkConditions += `, ${networkPreset.config.packetLoss * 100}% packet loss`;
  }

  // Add jitter if present
  if (networkPreset.config.jitter) {
    networkConditions += `, ${networkPreset.config.jitter}ms jitter`;
  }

  console.log(networkConditions);
  console.log(`========================================\n`);

  const startTime = Date.now();
  const scraperFunction = SCRAPER_FUNCTIONS[provider.toLowerCase()];

  if (!scraperFunction) {
    throw new Error(`Unknown provider: ${provider}. Available providers: ${Object.keys(SCRAPER_FUNCTIONS).join(', ')}`);
  }

  try {
    // Run the scraper with network throttling config
    // Note: Only the Autonet scraper currently accepts networkThrottling directly
    // For other scrapers, we'll need to modify their browser launch function
    const results = await scraperFunction(productCode, networkPreset.config);

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`\n✅ Test completed in ${duration.toFixed(2)} seconds`);
    console.log(`📊 Found ${results ? results.length : 0} products`);

    if (results && results.length > 0) {
      console.log(`📋 Sample result:`);
      console.log(JSON.stringify(results[0], null, 2));
    }

    return {
      provider,
      networkPreset: networkPreset.name,
      success: true,
      duration,
      resultCount: results ? results.length : 0,
      results: results || [], // Include the actual results for comparison
      metrics: {
        totalDuration: duration,
        networkConditions: {
          downloadSpeed: networkPreset.config.downloadSpeed / 1024, // KB/s
          uploadSpeed: networkPreset.config.uploadSpeed / 1024,     // KB/s
          latency: networkPreset.config.latency,                    // ms
          packetLoss: networkPreset.config.packetLoss || 0,         // percentage
          jitter: networkPreset.config.jitter || 0                  // ms
        }
      }
    };
  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.error(`\n❌ Test failed after ${duration.toFixed(2)} seconds`);
    console.error(`Error: ${error.message}`);

    return {
      provider,
      networkPreset: networkPreset.name,
      success: false,
      duration,
      error: error.message,
      metrics: {
        totalDuration: duration,
        failurePoint: error.message,
        networkConditions: {
          downloadSpeed: networkPreset.config.downloadSpeed / 1024, // KB/s
          uploadSpeed: networkPreset.config.uploadSpeed / 1024,     // KB/s
          latency: networkPreset.config.latency,                    // ms
          packetLoss: networkPreset.config.packetLoss || 0,         // percentage
          jitter: networkPreset.config.jitter || 0                  // ms
        }
      }
    };
  }
}

/**
 * Compare product results across different network conditions
 * @param {Array} results - Results from all network tests
 * @returns {Object} - Comparison results
 */
function compareResults(results) {
  const successfulResults = results.filter(r => r.success && r.results && r.results.length > 0);

  if (successfulResults.length <= 1) {
    return {
      hasDiscrepancies: false,
      message: "Not enough successful results to compare"
    };
  }

  // Use the first result as a reference
  const referenceResult = successfulResults[0];
  const discrepancies = [];

  // Compare each result with the reference
  for (let i = 1; i < successfulResults.length; i++) {
    const currentResult = successfulResults[i];
    const comparisonResult = compareProductLists(
      referenceResult.results,
      currentResult.results,
      referenceResult.networkPreset,
      currentResult.networkPreset
    );

    if (comparisonResult.hasDiscrepancies) {
      discrepancies.push(comparisonResult);
    }
  }

  return {
    hasDiscrepancies: discrepancies.length > 0,
    discrepancies,
    message: discrepancies.length > 0
      ? `Found ${discrepancies.length} network conditions with discrepancies`
      : "All results are consistent across network conditions"
  };
}

/**
 * Compare two lists of products
 * @param {Array} list1 - First list of products
 * @param {Array} list2 - Second list of products
 * @param {string} name1 - Name of the first list
 * @param {string} name2 - Name of the second list
 * @returns {Object} - Comparison results
 */
function compareProductLists(list1, list2, name1, name2) {
  const differences = [];

  // Check if the number of products is different
  if (list1.length !== list2.length) {
    differences.push({
      type: "count",
      message: `Different number of products: ${name1} has ${list1.length}, ${name2} has ${list2.length}`
    });
  }

  // Find the minimum length to compare
  const minLength = Math.min(list1.length, list2.length);

  // Compare each product
  for (let i = 0; i < minLength; i++) {
    const product1 = list1[i];
    const product2 = list2[i];

    // Compare important fields
    const fieldDifferences = [];

    // Fields to compare
    const fieldsToCompare = [
      'productCode', 'internalCode', 'brand', 'name', 'retailPrice',
      'exchangeValue', 'availability', 'returnable'
    ];

    for (const field of fieldsToCompare) {
      if (JSON.stringify(product1[field]) !== JSON.stringify(product2[field])) {
        fieldDifferences.push({
          field,
          value1: product1[field],
          value2: product2[field]
        });
      }
    }

    if (fieldDifferences.length > 0) {
      differences.push({
        type: "product",
        index: i,
        productCode: product1.productCode || product2.productCode,
        differences: fieldDifferences
      });
    }
  }

  return {
    hasDiscrepancies: differences.length > 0,
    list1Name: name1,
    list2Name: name2,
    differences
  };
}

/**
 * Generate a comprehensive report of all network tests
 * @param {Array} results - Results from all network tests
 * @param {number} totalDuration - Total duration of all tests
 * @param {string} provider - Provider name that was tested
 * @param {string} productCode - Product code that was searched
 * @param {Object} comparisonResults - Results of comparing products across network conditions
 */
function generateReport(results, totalDuration, provider, productCode, comparisonResults) {
  console.log('\n========================================');
  console.log('📊 COMPREHENSIVE TEST RESULTS SUMMARY');
  console.log('========================================');
  console.log(`Provider: ${provider.toUpperCase()}`);
  console.log(`Product Code: ${productCode}`);
  console.log(`Total Duration: ${totalDuration.toFixed(2)} seconds`);
  console.log(`Tests Run: ${results.length}\n`);

  // Calculate success rate
  const successfulTests = results.filter(r => r.success).length;
  const successRate = (successfulTests / results.length) * 100;
  console.log(`Success Rate: ${successRate.toFixed(2)}% (${successfulTests}/${results.length} tests passed)\n`);

  // Define column widths - account for emoji width in status column
  const colWidths = {
    name: 25,
    status: 12, // Increased to accommodate emoji width
    duration: 10,
    products: 8
  };

  // Table header with proper padding
  console.log(
    'Network Condition'.padEnd(colWidths.name) +
    '| Status   '.padEnd(colWidths.status + 3) + // Added spaces to better align with emoji width
    '| Duration'.padEnd(colWidths.duration + 2) +
    '| Products'
  );

  // Separator line with proper lengths
  console.log(
    '-'.repeat(colWidths.name) +
    '|' + '-'.repeat(colWidths.status + 2) +
    '|' + '-'.repeat(colWidths.duration + 1) +
    '|' + '-'.repeat(colWidths.products)
  );

  // Table rows with proper padding
  for (const result of results) {
    const status = result.success ? '✅ Success' : '❌ Failed';
    const duration = result.duration.toFixed(2) + 's';
    const products = result.success ? result.resultCount : 'N/A';

    // Handle long network preset names by truncating if necessary
    let nameCol;
    if (result.networkPreset.length > colWidths.name - 3 && result.networkPreset.length > colWidths.name) {
      // Truncate and add ellipsis for very long names
      nameCol = result.networkPreset.substring(0, colWidths.name - 3) + '...';
    } else {
      nameCol = result.networkPreset;
    }

    // Format to align columns with proper padding
    nameCol = nameCol.padEnd(colWidths.name);
    const statusCol = status.padEnd(colWidths.status);
    const durationCol = duration.toString().padEnd(colWidths.duration);

    console.log(`${nameCol}| ${statusCol}| ${durationCol}| ${products}`);
  }

  console.log('\n========================================');
  console.log('📈 PERFORMANCE ANALYSIS');
  console.log('========================================');

  // Find fastest and slowest tests
  const successfulResults = results.filter(r => r.success);
  if (successfulResults.length > 0) {
    const fastest = successfulResults.reduce((min, r) =>
      r.duration < min.duration ? r : min,
      { duration: Infinity }
    );

    const slowest = successfulResults.reduce((max, r) =>
      r.duration > max.duration ? r : max,
      { duration: -Infinity }
    );

    console.log(`Fastest Test: ${fastest.networkPreset} (${fastest.duration.toFixed(2)}s)`);
    console.log(`Slowest Test: ${slowest.networkPreset} (${slowest.duration.toFixed(2)}s)`);
    console.log(`Speed Difference: ${(slowest.duration / fastest.duration).toFixed(2)}x slower`);

    // Calculate average duration
    const avgDuration = successfulResults.reduce((sum, r) => sum + r.duration, 0) / successfulResults.length;
    console.log(`Average Test Duration: ${avgDuration.toFixed(2)}s`);

    // Calculate total CPU time used (same as total duration since we're running sequentially)
    const totalCpuTime = successfulResults.reduce((sum, r) => sum + r.duration, 0);
    console.log(`Total CPU Time: ${totalCpuTime.toFixed(2)}s`);
  }

  // Check if any tests failed
  const failedTests = results.filter(r => !r.success);
  if (failedTests.length > 0) {
    console.log('\n❌ Failed Tests:');
    failedTests.forEach(r => {
      console.log(`   - ${r.networkPreset}: ${r.error}`);
    });
  }

  // Display comparison results
  if (comparisonResults) {
    console.log('\n========================================');
    console.log('🔍 PRODUCT COMPARISON ACROSS NETWORK CONDITIONS');
    console.log('========================================');

    console.log(comparisonResults.message);

    if (comparisonResults.hasDiscrepancies) {
      console.log('\nDiscrepancies found:');

      comparisonResults.discrepancies.forEach((discrepancy, index) => {
        console.log(`\n${index + 1}. Between ${discrepancy.list1Name} and ${discrepancy.list2Name}:`);

        discrepancy.differences.forEach(diff => {
          if (diff.type === 'count') {
            console.log(`   - ${diff.message}`);
          } else if (diff.type === 'product') {
            console.log(`   - Product #${diff.index + 1} (${diff.productCode || 'Unknown'}) has differences:`);

            diff.differences.forEach(fieldDiff => {
              console.log(`     * ${fieldDiff.field}: "${fieldDiff.value1}" vs "${fieldDiff.value2}"`);
            });
          }
        });
      });
    }
  }

  // Recommendations
  console.log('\n🔧 RECOMMENDATIONS');
  console.log('------------------');

  if (successRate === 100) {
    console.log(`✅ The ${provider.toUpperCase()} scraper is highly resilient to network conditions!`);
    console.log('   - It successfully handled all tested network scenarios');

    // Check if there's significant slowdown
    if (successfulResults.length > 1) {
      // Get fastest and slowest tests directly from the variables we already have
      const fastestTest = successfulResults.reduce((min, r) =>
        r.duration < min.duration ? r : min,
        { duration: Infinity }
      );

      const slowestTest = successfulResults.reduce((max, r) =>
        r.duration > max.duration ? r : max,
        { duration: -Infinity }
      );

      // Find the network preset configurations
      let fastestSpeed = 0;
      let slowestSpeed = 0;

      for (const [_, preset] of Object.entries(NETWORK_PRESETS)) {
        if (preset.name === fastestTest.networkPreset) {
          fastestSpeed = preset.config.downloadSpeed;
        }
        if (preset.name === slowestTest.networkPreset) {
          slowestSpeed = preset.config.downloadSpeed;
        }
      }

      if (fastestSpeed > 0 && slowestSpeed > 0) {
        const speedRatio = fastestSpeed / slowestSpeed;
        const durationRatio = slowestTest.duration / fastestTest.duration;

        if (durationRatio < speedRatio / 2) {
          console.log('   - Performance scales well with network conditions');
          console.log(`   - Despite a ${speedRatio.toFixed(0)}x difference in network speed, execution was only ${durationRatio.toFixed(2)}x slower`);
        } else {
          console.log('   - Consider implementing more aggressive caching for slow networks');
        }
      }
    }
  } else {
    console.log(`⚠️ The ${provider.toUpperCase()} scraper needs improvements to handle poor network conditions:`);

    // Analyze which network conditions caused failures
    const failedConditions = failedTests.map(r => {
      const presetKey = Object.keys(NETWORK_PRESETS).find(
        key => NETWORK_PRESETS[key].name === r.networkPreset
      );
      return NETWORK_PRESETS[presetKey];
    });

    // Check if all failures had high latency
    const allHighLatency = failedConditions.every(preset => preset.config.latency > 500);
    if (allHighLatency) {
      console.log('   - Increase timeout values for high-latency connections');
      console.log('   - Implement retry mechanisms for network requests');
    }

    // Check if all failures had low bandwidth
    const allLowBandwidth = failedConditions.every(preset => preset.config.downloadSpeed < 100 * 1024);
    if (allLowBandwidth) {
      console.log('   - Optimize payload size for low-bandwidth connections');
      console.log('   - Implement progressive loading of content');
    }

    // Check if all failures had packet loss
    const allPacketLoss = failedConditions.every(preset => preset.config.packetLoss > 0);
    if (allPacketLoss) {
      console.log('   - Implement more robust error handling for intermittent connections');
      console.log('   - Add automatic retry logic for failed requests');
    }
  }
}

/**
 * Main function to run all network tests sequentially
 */
async function runNetworkTests(provider, productCode) {
  console.log(`🚀 Starting network throttling tests for ${provider.toUpperCase()} with product code: ${productCode}`);
  console.log(`🌐 Will test with ${Object.keys(NETWORK_PRESETS).length} different network conditions\n`);

  const startTime = Date.now();
  const results = [];

  // Run network tests sequentially to avoid resource conflicts
  for (const [_, preset] of Object.entries(NETWORK_PRESETS)) {
    // Add a small delay between tests to ensure resources are freed
    if (results.length > 0) {
      console.log('\nWaiting 5 seconds before starting next test...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    const result = await runScraperWithNetworkConditions(provider, productCode, preset);
    results.push(result);
  }

  const totalDuration = (Date.now() - startTime) / 1000;

  // Compare results across different network conditions
  const comparisonResults = compareResults(results);

  // Generate comprehensive summary report
  generateReport(results, totalDuration, provider, productCode, comparisonResults);

  return results;
}

// Get provider and product code from command line arguments
const provider = process.argv[2];
const productCode = process.argv[3] || '**********'; // Default product code if not provided

if (!provider) {
  console.error('❌ Error: Provider name is required');
  console.log('Usage: node test-network.js <provider> <product-code>');
  console.log('Available providers:', Object.keys(SCRAPER_FUNCTIONS).join(', '));
  process.exit(1);
}

if (!SCRAPER_FUNCTIONS[provider.toLowerCase()]) {
  console.error(`❌ Error: Unknown provider "${provider}"`);
  console.log('Available providers:', Object.keys(SCRAPER_FUNCTIONS).join(', '));
  process.exit(1);
}

// Start the network tests
runNetworkTests(provider, productCode).catch(console.error);