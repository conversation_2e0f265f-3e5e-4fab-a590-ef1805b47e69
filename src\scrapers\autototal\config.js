const path = require('path');

// Configuration for the Autototal scraper
module.exports = {
  SCRAPER_NAME: 'AUTOTOTAL',
  
  // Paths
  STATE_DIR: path.resolve(__dirname, 'state'),
  get COOKIES_PATH() { return path.join(this.STATE_DIR, 'cookies.json') },
  get SESSION_URL_PATH() { return path.join(this.STATE_DIR, 'session-url.txt') },
  get PROFILE_PATH() { return path.join(this.STATE_DIR, 'profile') },
  
  // URLs
  LOGIN_URL: 'https://www3.eoriginal.ro/login',
  DEFAULT_TEST_PAGE: 'https://www3.eoriginal.ro/#/',
  
  // Selectors
  SELECTORS: {
    LOGIN: {
      LOGGED_IN: '#userMenuBtn',
      USERNAME: 'input[name="username"]',
      PASSWORD: 'input[name="password"]',
      LOGIN_BUTTON: 'button[type="submit"].btn-primary',
      DEVICE_NAME: 'input[name="deviceName"]',
      DEVICE_SAVE_BUTTON: 'button[type="submit"].btn-primary',
    },
    SEARCH: {
      INPUT: '#searchBoxIbcs',
      BUTTON: 'button.src_btn',
      RESULTS_TABLE: '.art_wrapper',
      PRICE: '.price_final > span',
      PRICE_REQUEST_BUTTON: 'button.btn-lightx.bold',
      PRODUCT_CARDS: '.art_wrapper .article-card',
      TITLE: 'h6.card-title span',
      DESCRIPTION: '.card-descr span',
      DELIVERY: '.stoc_texts',
      EXCHANGE_VALUE_WRAPPER: 'small.piesas-wrapper',
    },
    PAGINATION: {
      CONTAINER: 'ul.pagination',
      ITEM: 'li.page-item',
      LINK: 'a.page-link',
      ACTIVE: 'li.page-item.active',
      NEXT: 'li.page-item:last-child a.page-link',
    }
  },
  
  // Timeouts and limits
  MAX_PAGES_TO_VISIT: 50,
  SEARCH_RESULTS_TIMEOUT: 20000,
  PRICE_INFO_TIMEOUT: 30000,
  LOGIN_CHECK_TIMEOUT: 1000,
  NAVIGATION_TIMEOUT: 60000,
  PAGINATION_CHECK_TIMEOUT: 5000,
  CONTENT_CHANGE_TIMEOUT: 5000,
  
  // Delays
  DELAYS: {
    AFTER_PAGE_LOAD: [500, 1000],
    BETWEEN_INPUTS: [300, 600],
    AFTER_NAVIGATION: [1000, 1500],
    RETRY_DELAY: [1000, 2000],
    MINIMAL_DELAY: 50,
  },
  
  // Retry settings
  MAX_RETRIES: 3,
};
