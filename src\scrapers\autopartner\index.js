const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });

const { launchBrowser } = require('../../shared/browser');
const {
  saveCookiesToFile,
  loadCookiesFromFile,
  saveSessionUrlToFile,
  loadSessionUrlFromFile,
} = require('../../shared/session');
const { randomDelay, sleep } = require('../../shared/utils');

const SCRAPER_NAME = 'AUTOPARTNER';
const STATE_DIR = path.resolve(__dirname, 'state');
const COOKIES_PATH = path.join(STATE_DIR, 'cookies.json');
const SESSION_URL_PATH = path.join(STATE_DIR, 'session-url.txt');
const PROFILE_PATH = path.join(STATE_DIR, 'profile');

const LOGIN_URL = 'https://apcat.eu';
const DEFAULT_TEST_PAGE = 'https://apcat.eu';
const LOGGED_IN_SELECTOR = 'span.usrNameLable';
const USERNAME_SELECTOR = 'input#username';
const PASSWORD_SELECTOR = 'input#password';
const LOGIN_BUTTON_SELECTOR = 'input#login';


const RESULTS_TABLE_SELECTOR = '#main_artikel_panel_maintable';
const PRICE_SELECTOR = 'td.tbl_al_erp_price_spalte_prVal span[pricefiltercat="0"]';

/**
 * Main function to run the Autopartner scraper.
 * @param {string} productCode - The product code to search for.
 * @param {object} [networkThrottling=null] - Optional network throttling configuration.
 *   When null (default), no throttling is applied.
 *   When provided, should contain: {downloadSpeed, uploadSpeed, latency} in bytes/second and milliseconds.
 * @returns {Promise<Array|null>} Array of scraped products or null if scraping failed.
 */
async function runAutopartnerScraper(productCode, networkThrottling = null) {
  console.log(`🚀 Starting ${SCRAPER_NAME} scraper for code: ${productCode}`);
  if (networkThrottling) {
    console.log(`🌐 Network throttling enabled: ↓${networkThrottling.downloadSpeed/1024}KB/s, ↑${networkThrottling.uploadSpeed/1024}KB/s, ${networkThrottling.latency}ms latency`);
  }

  let browser;
  try {
    const { browser: launchedBrowser, page } = await launchBrowser({
      userDataDir: PROFILE_PATH,
      networkThrottling
    });
    browser = launchedBrowser;

    let testPage = loadSessionUrlFromFile(SESSION_URL_PATH) || DEFAULT_TEST_PAGE;
    await loadCookiesFromFile(page, COOKIES_PATH);
    await page.goto(testPage, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await sleep(randomDelay(500, 1000));

    console.log('🔐 Not logged in or session expired. Performing login...');

    const loginFrame = page.frames().find(f => f.url().includes('Login.aspx'));

    if (!loginFrame) {
      throw new Error('❌ Login frame not found!');
    }

    await loginFrame.waitForSelector(USERNAME_SELECTOR, { timeout: 5000 });
    await loginFrame.type(USERNAME_SELECTOR, process.env.AUTOPARTNER_USER, { delay: randomDelay(100, 200) });
    await sleep(randomDelay(300, 600));
    await loginFrame.type(PASSWORD_SELECTOR, process.env.AUTOPARTNER_PASSWORD, { delay: randomDelay(100, 200) });
    await sleep(randomDelay(300, 600));
    await loginFrame.click(LOGIN_BUTTON_SELECTOR);


    await sleep(1000); // wait a bit for possible navigation or prompt
    let frames = page.frames();
    frames.forEach(f => console.log('🧩 Frame:', f.url()));
    // Re-fetch frame in case it was refreshed/reloaded
    let newLoginFrame = page.frames().find(f => f.url().includes('Login.aspx'));

    try {
      await sleep(1000);
      const sessionOverrideButton = await newLoginFrame.waitForSelector('input#ok', { timeout: 10000 });
      if (sessionOverrideButton) {
        console.log('⚠️ Session already active elsewhere — confirming override...');
        await sessionOverrideButton.click();
      }
    } catch (e) {
      console.log('✅ No session override prompt detected.');
    }

    let mainframe;
    const start = Date.now();
    while (!mainframe && Date.now() - start < 10000) {
      await sleep(500);
      mainframe = page.frames().find(f => f.url().includes('default.aspx'));
    }

    if (!mainframe) {
      const frameUrls = page.frames().map(f => f.url());
      console.log('🔍 Available frames:', frameUrls);
      throw new Error('❌ Mainframe frame not found');
    }

    try {
      await mainframe.waitForSelector(LOGGED_IN_SELECTOR, { timeout: 5000 });
      console.log('✅ Logged in successfully!');
      await saveCookiesToFile(page, COOKIES_PATH);
      saveSessionUrlToFile(page.url(), SESSION_URL_PATH);
    } catch {
      throw new Error('❌ Logged-in confirmation selector not found. Login may have failed.');
    }

    await sleep(500);

    let notificationFrame;
    const notifStart = Date.now();
    while (!notificationFrame && Date.now() - notifStart < 1500) {
      await sleep(250);
      notificationFrame = page.frames().find(f => f.url().includes('Notification.aspx'));
    }

    if (notificationFrame) {
      console.log('🔔 Notification detected, attempting to close it...');
      try {
        const closeBtnSelector = 'div.notificationBtn.btn';
        await notificationFrame.waitForSelector(closeBtnSelector, { timeout: 5000 });
        const closeBtn = await notificationFrame.$(closeBtnSelector);
        if (closeBtn) {
          await closeBtn.click();
          console.log('🔄 Waiting for notification frame to be removed...');

          const notifDisappearStart = Date.now();
          while (true) {
            await sleep(300);
            const stillExists = page.frames().some(f => f.url().includes('Notification.aspx'));
            if (!stillExists) break;
            if (Date.now() - notifDisappearStart > 10000) {
              console.warn('⚠️ Notification frame did not disappear after 10s.');
              break;
            }
          }
          console.log('✅ Notification frame no longer visible.');
        } else {
          console.warn('⚠️ Close button not found in notification frame.');
        }
      } catch (e) {
        console.warn('⚠️ Error while trying to close notification:', e.message);
      }
    } else {
      console.log('✅ No notification overlay detected.');
    }

    const mainFrame = page.frames().find(f => f.url().includes('default.aspx'));

    if (!mainFrame) {
      throw new Error('❌ Main search frame not found!');
    }

    await sleep(randomDelay(300, 600));

    const SEARCH_INPUT_SELECTOR = 'input#tp_articlesearch_txt_articleSearch';
    const SEARCH_BUTTON_SELECTOR = 'input#tp_articlesearch_articleSearch_imgBtn';

    console.log(`🔎 Searching for product code: ${productCode}`);
    await mainFrame.waitForSelector(SEARCH_INPUT_SELECTOR, { timeout: 5000 });
    await mainFrame.type(SEARCH_INPUT_SELECTOR, productCode, { delay: randomDelay(100, 200) });
    await sleep(randomDelay(300, 600));

    await mainFrame.click(SEARCH_BUTTON_SELECTOR);
    console.log('🚀 Search initiated.');


    // await sleep(5000);
    // frames = page.frames();
    // frames.forEach(f => console.log('🧩 Frame:', f.url()));


    // Wait for new results frame
    let resultFrame;
    const resultStart = Date.now();
    while (!resultFrame && Date.now() - resultStart < 15000) {
      await sleep(500);
      resultFrame = page.frames().find(f => f.url().includes('default.aspx') && f.url().includes('txt_articleSearch='));
    }
    if (!resultFrame) throw new Error('❌ Results frame not found after search');

    await resultFrame.waitForSelector(RESULTS_TABLE_SELECTOR, { timeout: 15000 });
    console.log('📋 Results table loaded. Waiting for product details to fully load...');

    console.log('⏳ Waiting for visible product prices to load...');
    const maxWaitTime = 20000;
    const pollInterval = 500;
    const startTime = Date.now();

    let allReady = false;
    while (Date.now() - startTime < maxWaitTime) {
      const { total, ready } = await resultFrame.evaluate((tableSel) => {
        const rows = Array.from(document.querySelectorAll(`${tableSel} tr.main_artikel_panel_tr_artikel`));
        let visibleCount = 0;
        let readyCount = 0;

        for (const row of rows) {
          const mainCode = row.querySelector('.pnl_link_haendlernr a')?.textContent.trim();
          if (!mainCode) continue;

          visibleCount++;

          const priceRows = row.querySelectorAll('table.tbl_al_erp_price tr');
          for (const tr of priceRows) {
            const label = tr.querySelector('td.tbl_al_erp_price_spalte_prBez span')?.textContent.trim();
            const value = tr.querySelector('td.tbl_al_erp_price_spalte_prVal span')?.textContent.trim();
            if (label === 'Gross detail:' && value && value.length > 0) {
              readyCount++;
              break;
            }
          }
        }

        return { total: visibleCount, ready: readyCount };
      }, RESULTS_TABLE_SELECTOR);

      console.log(`📦 Valid product rows: ${total}, Ready: ${ready}`);

      if (total > 0 && ready === total) {
        allReady = true;
        break;
      }

      await sleep(pollInterval);
    }

    if (!allReady) {
      console.warn('⚠️ Not all prices loaded within time — proceeding with partial data.');
    } else {
      console.log('✅ All visible product rows loaded with prices.');
    }

    const results = await resultFrame.evaluate((tableSel, priceSel) => {
      const rows = Array.from(document.querySelectorAll(`${tableSel} tr`));
      const products = [];
      let currentName = null;
      let currentBrand = null;

      for (const row of rows) {
        if (row.classList.contains('main_artikel_panel_tr_genart')) {
          const nameSpan = row.querySelector('span');
          currentName = nameSpan ? nameSpan.textContent.trim() : 'N/A';
        } else if (row.classList.contains('main_artikel_panel_tr_einspeiser')) {
          const brandSpan = row.querySelector('td[colspan] span');
          currentBrand = brandSpan ? brandSpan.textContent.trim() : 'N/A';
        } else if (row.classList.contains('main_artikel_panel_tr_artikel')) {
          const internalCode = row.querySelector('.pnl_link_haendlernr a')?.textContent.trim();
          const productCode = row.querySelector('.pnl_link_eartnr a')?.textContent.trim();

          let retailPrice = null;
          let exchangeValue = null;
          const priceRows = row.querySelectorAll('table.tbl_al_erp_price tr');
          for (const tr of priceRows) {
            const label = tr.querySelector('td.tbl_al_erp_price_spalte_prBez span')?.textContent.trim();
            if (label === 'Gross detail:') {
              const priceText = tr.querySelector('td.tbl_al_erp_price_spalte_prVal span')?.textContent.trim();
              if (priceText) {
                // Extract numeric part (e.g., "2971,21 RON" -> "2971,21")
                const match = priceText.match(/([\d.,]+)/);
                if (match) {
                  // Handle European number format (2971,21)
                  // First, remove all dots (thousands separators)
                  // Then, replace comma with dot (decimal separator)
                  const cleanedNumber = match[1].replace(/\./g, '').replace(',', '.');
                  const numeric = parseFloat(cleanedNumber);
                  if (!isNaN(numeric)) {
                    retailPrice = numeric;
                  } else {
                    retailPrice = priceText;
                  }
                } else {
                  retailPrice = priceText;
                }
              }
            }
            if (label === 'Gross deposit:') {
              const exchangeText = tr.querySelector('td.tbl_al_erp_price_spalte_prVal span')?.textContent.trim();
              if (exchangeText) {
                // If the exchange value is just "-", set it to null
                if (exchangeText === "-") {
                  exchangeValue = null;
                } else {
                  // Extract numeric part (e.g., "363,18 RON" -> "363,18")
                  const match = exchangeText.match(/([\d.,]+)/);
                  if (match) {
                    // Handle European number format (363,18)
                    // First, remove all dots (thousands separators)
                    // Then, replace comma with dot (decimal separator)
                    const cleanedNumber = match[1].replace(/\./g, '').replace(',', '.');
                    const numeric = parseFloat(cleanedNumber);
                    if (!isNaN(numeric)) {
                      exchangeValue = numeric;
                    } else {
                      exchangeValue = exchangeText;
                    }
                  } else {
                    exchangeValue = exchangeText;
                  }
                }
              }
            }
          }

          let delivery = row.querySelector('.erp_availIcon_toolTipPanel span')?.textContent.trim() || '';

          if (delivery.includes('Not available')) {
            delivery = 'Not available';
          } else {
            const match = delivery.match(/Estimated date of shipment: (\d{2}\.\d{2}\.\d{4} \d{2}:\d{2}:\d{2})/);
            delivery = match ? match[1] : null;
          }

          // Extract stock information from the table
          let availability = null;
          const stockTable = row.querySelector('#tbl_Erp_Info_Stock');
          if (stockTable) {
            // Get the headers (th elements)
            const headers = Array.from(stockTable.querySelectorAll('tr.thr_title_Stock th')).map(th => th.textContent.trim());

            // Get the values (td elements)
            const values = Array.from(stockTable.querySelectorAll('tr.tr_desc_Stock td span')).map(td => td.textContent.trim());

            // Combine headers and values, but skip the "CN" column
            if (headers.length === values.length && headers.length > 0) {
              const stockInfo = headers
                .map((header, index) => {
                  // Skip the "CN" column
                  if (header === "CN") return null;
                  return `${header}:${values[index]}`;
                })
                .filter(item => item !== null) // Remove null entries (skipped columns)
                .join('/');

              // Check if all stock values are empty, "--", or other indicators of no availability
              const validStockValues = headers
                .map((header, index) => {
                  // Skip the "CN" column
                  if (header === "CN") return null;
                  const value = values[index];
                  // Consider a value valid if it's not empty, not "--", not "0", and not just whitespace
                  return value && value !== "--" && value !== "0" && value.trim() !== "";
                })
                .filter(item => item !== null && item !== false); // Remove null entries and false values

              // If no valid stock values exist, set availability to null
              if (validStockValues.length === 0) {
                availability = null;
              } else {
                availability = stockInfo;
              }
            }
          }

          if (productCode) {
            // Determine availabilitySummary based on availability field
            // For AUTOPARTNER: "Available" if availability has value, "Unavailable" if null
            const availabilitySummary = availability !== null ? "Available" : "Unavailable";

            products.push({
              name: currentName || null,
              brand: currentBrand || null,
              productCode: productCode || null,
              internalCode: internalCode || null,
              delivery: delivery,
              retailPrice: retailPrice,
              exchangeValue: exchangeValue,
              availability: availability,
              availabilitySummary: availabilitySummary,
              returnable: true,
              provider: 'AUTOPARTNER'
            });
          }
        }
      }
      return products;
    }, RESULTS_TABLE_SELECTOR, PRICE_SELECTOR);

    // await browser.close();
    console.log(`✅ ${SCRAPER_NAME} scraper finished successfully.`);
    return results;

  } catch (err) {
    console.error(`❌ Error in ${SCRAPER_NAME}:`, err);
    if (browser) await browser.close();
    return null;
  }
}

module.exports = {
  runAutopartnerScraper,
};

