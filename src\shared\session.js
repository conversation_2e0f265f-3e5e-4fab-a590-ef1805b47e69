const fs = require('fs');
const path = require('path');

/**
 * Saves the current page cookies to a specified file path.
 * Ensures the directory exists before writing.
 * @param {import('puppeteer').Page} page - The Puppeteer page instance.
 * @param {string} cookiesPath - The file path to save cookies to.
 */
async function saveCookiesToFile(page, cookiesPath) {
  try {
    const cookies = await page.cookies();
    const dir = path.dirname(cookiesPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(cookiesPath, JSON.stringify(cookies, null, 2));
    console.log(`💾 Cookies saved to ${cookiesPath}`);
  } catch (err) {
    console.error(`❌ Error saving cookies to ${cookiesPath}:`, err);
  }
}

/**
 * Loads cookies from a specified file path and applies them to the page.
 * @param {import('puppeteer').Page} page - The Puppeteer page instance.
 * @param {string} cookiesPath - The file path to load cookies from.
 * @returns {Promise<boolean>} - True if cookies were loaded successfully, false otherwise.
 */
async function loadCookiesFromFile(page, cookiesPath) {
  if (fs.existsSync(cookiesPath)) {
    try {
      const raw = fs.readFileSync(cookiesPath);
      if (!raw || raw.length === 0) throw new Error('Empty cookie file');

      const cookies = JSON.parse(raw);
      await page.setCookie(...cookies);
      console.log(`🍪 Cookies loaded from ${cookiesPath}`);
      return true;
    } catch (err) {
      console.warn(`⚠️ Failed to load cookies from ${cookiesPath}:`, err.message);
      // Optionally delete corrupted cookie file?
      // fs.unlinkSync(cookiesPath);
      return false;
    }
  }
  return false;
}

/**
 * Saves a session URL to a specified file path.
 * Ensures the directory exists before writing.
 * @param {string} sessionUrl - The URL to save.
 * @param {string} sessionUrlPath - The file path to save the URL to.
 */
function saveSessionUrlToFile(sessionUrl, sessionUrlPath) {
  try {
    const dir = path.dirname(sessionUrlPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(sessionUrlPath, sessionUrl);
    console.log(`🔗 Session URL saved to ${sessionUrlPath}`);
  } catch (err) {
    console.error(`❌ Error saving session URL to ${sessionUrlPath}:`, err);
  }
}

/**
 * Loads a session URL from a specified file path.
 * @param {string} sessionUrlPath - The file path to load the URL from.
 * @returns {string | null} - The loaded URL or null if the file doesn't exist or is empty.
 */
function loadSessionUrlFromFile(sessionUrlPath) {
  if (fs.existsSync(sessionUrlPath)) {
    try {
      const savedUrl = fs.readFileSync(sessionUrlPath, 'utf-8');
      if (savedUrl && savedUrl.trim().length > 0) {
        console.log(`🔗 Session URL loaded from ${sessionUrlPath}`);
        return savedUrl.trim();
      }
    } catch (err) {
      console.warn(`⚠️ Failed to load session URL from ${sessionUrlPath}:`, err.message);
    }
  }
  return null;
}


module.exports = {
  saveCookiesToFile,
  loadCookiesFromFile,
  saveSessionUrlToFile,
  loadSessionUrlFromFile,
};