const { runAutonetScraper } = require('./src/scrapers/autonet/index');
const { runAutototalScraperModular } = require('./src/scrapers/autototal/index');
const { runIntercarsScraper } = require('./src/scrapers/intercars/index');
const { runAutopartnerScraper } = require('./src/scrapers/autopartner/index');
const { runMateromScraper } = require('./src/scrapers/materom/index');
// AJS scraper excluded as requested
const { runElitScraper } = require('./src/scrapers/elit/index');
const { runBardiScraper } = require('./src/scrapers/bardi/index');
const { SUPP } = require('./src/shared/suppliers');

/**
 * Run all scrapers in parallel for a given product code
 * @param {string} productCode - The product code to search for
 * @returns {Promise<Object>} - Object containing results from all scrapers
 */
async function runAllScrapers(productCode) {
  console.log(`🚀 Starting multi-scraper for product code: ${productCode}`);
  console.log('⏳ Running all scrapers in parallel...');

  // Define all scrapers with their corresponding functions and names
  const scrapers = [
    { name: SUPP.AUTONET, fn: runAutonetScraper },
    { name: SUPP.AUTOTOTAL, fn: runAutototalScraperModular },
    { name: SUPP.INTERCARS, fn: runIntercarsScraper },
    { name: SUPP.AUTOPARTNER, fn: runAutopartnerScraper },
    { name: SUPP.MATEROM, fn: runMateromScraper },
    //{ name: SUPP.AJS, fn: runAjsScraper },
    { name: SUPP.ELIT, fn: runElitScraper },
    { name: SUPP.BARDI, fn: runBardiScraper }
  ];

  // Run all scrapers in parallel using Promise.allSettled
  const results = await Promise.allSettled(
    scrapers.map(async (scraper) => {
      console.log(`🔍 Starting ${scraper.name} scraper...`);
      try {
        const startTime = Date.now();
        const result = await scraper.fn(productCode);
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000; // Convert to seconds

        return {
          supplier: scraper.name,
          success: true,
          data: result || [],
          count: result ? result.length : 0,
          duration: duration
        };
      } catch (error) {
        console.error(`❌ Error in ${scraper.name} scraper:`, error);
        return {
          supplier: scraper.name,
          success: false,
          error: error.message,
          data: [],
          count: 0,
          duration: 0
        };
      }
    })
  );

  // Process results
  const processedResults = {};

  results.forEach((result, index) => {
    const scraperName = scrapers[index].name;

    if (result.status === 'fulfilled') {
      processedResults[scraperName] = result.value;
    } else {
      processedResults[scraperName] = {
        supplier: scraperName,
        success: false,
        error: result.reason ? result.reason.message : 'Unknown error',
        data: [],
        count: 0,
        duration: 0
      };
    }
  });

  return processedResults;
}

/**
 * Generate statistics from the scraper results
 * @param {Object} results - Results from all scrapers
 * @returns {Object} - Statistics object
 */
function generateStatistics(results) {
  const stats = {
    totalProducts: 0,
    totalSuccessfulScrapers: 0,
    totalFailedScrapers: 0,
    productsPerSupplier: {},
    totalDuration: 0,
    uniqueBrands: new Set(),
    errors: [], // Track errors for better reporting
    availability: {
      available: 0,
      unavailable: 0,
      unknown: 0
    }
  };

  // Process each supplier's results
  Object.keys(results).forEach(supplier => {
    const supplierResult = results[supplier];

    // Count successful/failed scrapers
    if (supplierResult.success) {
      stats.totalSuccessfulScrapers++;

      // Add to total duration
      stats.totalDuration += supplierResult.duration;

      // Process product data
      const products = supplierResult.data;
      const productCount = products.length;
      stats.totalProducts += productCount;
      stats.productsPerSupplier[supplier] = productCount;

      // Skip further processing if no products
      if (productCount === 0) return;

      // Count brands and availability
      products.forEach(product => {
        // Count brands
        if (product.brand) {
          stats.uniqueBrands.add(product.brand);
        }

        // Count availability
        if (product.availability) {
          const availText = String(product.availability).toLowerCase();
          if (availText.includes('0') || availText.includes('no') || availText === 'false' || availText === '') {
            stats.availability.unavailable++;
          } else {
            stats.availability.available++;
          }
        } else {
          stats.availability.unknown++;
        }
      });
    } else {
      stats.totalFailedScrapers++;
      // Track error information
      stats.errors.push({
        supplier,
        error: supplierResult.error || 'Unknown error'
      });
    }
  });

  // Convert Set to array for JSON serialization
  stats.uniqueBrands = Array.from(stats.uniqueBrands);

  // Calculate average duration
  stats.averageDuration = stats.totalSuccessfulScrapers > 0
    ? (stats.totalDuration / stats.totalSuccessfulScrapers).toFixed(2)
    : 0;

  return stats;
}

/**
 * Main function to run all scrapers and display results
 */
async function main() {
  // Get product code from command line arguments
  const productCode = process.argv[2];

  if (!productCode) {
    console.error('❌ Please provide a product code as a command line argument');
    console.log('Usage: node multi-scraper.js <product-code>');
    process.exit(1);
  }

  console.log(`=== Multi-Scraper for Product Code: ${productCode} ===\n`);

  try {
    // Run all scrapers
    const startTime = Date.now();
    const results = await runAllScrapers(productCode);
    const endTime = Date.now();
    const totalDuration = ((endTime - startTime) / 1000).toFixed(2); // Convert to seconds

    console.log(`\n=== Scraping Completed in ${totalDuration} seconds ===\n`);

    // Generate statistics
    const stats = generateStatistics(results);

    // Display high-level summary statistics
    console.log('=== Summary Statistics ===');
    console.log(`Total Products Found: ${stats.totalProducts}`);
    console.log(`Successful Scrapers: ${stats.totalSuccessfulScrapers}`);
    console.log(`Failed Scrapers: ${stats.totalFailedScrapers}`);
    console.log(`Average Scraper Duration: ${stats.averageDuration} seconds`);
    console.log(`Unique Brands Found: ${stats.uniqueBrands.length}`);
    console.log(`Available Products: ${stats.availability.available}`);
    console.log(`Unavailable Products: ${stats.availability.unavailable}`);
    console.log(`Unknown Availability: ${stats.availability.unknown}`);

    // Display products per supplier
    console.log('\n=== Products Per Supplier ===');
    Object.entries(stats.productsPerSupplier).forEach(([supplier, count]) => {
      console.log(`${supplier}: ${count} products`);
    });

    // Display any errors that occurred
    if (stats.errors.length > 0) {
      console.log('\n=== Scraper Errors ===');
      stats.errors.forEach(error => {
        console.log(`❌ ${error.supplier}: ${error.error}`);
      });
    }

    // Log full results JSON for debugging if needed
    console.log('\n=== Full Results JSON ===');
    console.log(JSON.stringify(results, null, 2));

  } catch (error) {
    console.error('❌ An unexpected error occurred:', error);
    process.exit(1);
  }
}

// Run the main function if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = { runAllScrapers, generateStatistics };
