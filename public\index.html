<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Scraper Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        brand: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        accent: {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            200: '#fed7aa',
                            300: '#fdba74',
                            400: '#fb923c',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                            800: '#9a3412',
                            900: '#7c2d12',
                        }
                    },
                    fontFamily: {
                        'sans': ['Poppins', 'sans-serif'],
                        'heading': ['Montserrat', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        /* Custom styles */
        .app-bar-gradient {
            background: linear-gradient(135deg, #0284c7 0%, #075985 100%);
        }
        .search-button-gradient {
            background: linear-gradient(90deg, #f97316 0%, #ea580c 100%);
        }
        .app-bar-shadow {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        /* Sticky footer */
        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        main {
            flex: 1;
        }

        /* Pulse animation for FAB */
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);
                transform: scale(1);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);
                transform: scale(1.05);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
                transform: scale(1);
            }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- App Bar -->
    <header class="app-bar-gradient text-white app-bar-shadow sticky top-0 z-10">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row md:items-center justify-between py-4">
                <!-- Logo and Title -->
                <div class="flex items-center space-x-4 mb-4 md:mb-0">
                    <div class="bg-white p-2 rounded-lg shadow-md">
                        <img src="/logo.png" alt="Company Logo" class="h-10">
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold font-heading tracking-tight">Multi-Scraper</h1>
                        <p class="text-xs text-brand-200">Unified product search across suppliers</p>
                    </div>
                </div>

                <!-- Search Form -->
                <form id="search-form" class="flex-1 max-w-xl mx-auto md:mx-4">
                    <div class="flex rounded-lg overflow-hidden shadow-lg border-2 border-white/20">
                        <input type="text" id="product-code" name="productCode"
                               placeholder="Enter product code (e.g., JRP675)" required
                               class="flex-grow px-4 py-3 focus:outline-none border-0 text-gray-800 font-medium">
                        <button type="submit" id="search-button"
                                class="search-button-gradient hover:opacity-90 text-white px-6 py-3 transition-all font-medium">
                            <i class="fas fa-search mr-2"></i>Search
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow">
        <div class="container mx-auto px-4 py-8 max-w-6xl">

        <div id="loading" class="hidden">
            <div class="flex flex-col items-center mb-8">
                <div class="animate-spin rounded-full h-16 w-16 border-b-4 border-accent-500 mb-4"></div>
                <p class="text-xl text-gray-700 font-medium">Searching across all suppliers...</p>
            </div>

            <div class="max-w-3xl mx-auto bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
                <h3 class="text-lg font-heading font-semibold mb-4 text-gray-800 flex items-center">
                    <i class="fas fa-server text-brand-600 mr-2"></i>Scraper Status
                </h3>
                <div id="scrapers-status" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Scraper status items will be added here dynamically -->
                </div>
            </div>

            <div class="max-w-3xl mx-auto bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                <h3 class="text-lg font-heading font-semibold mb-3 text-gray-800 flex items-center">
                    <i class="fas fa-terminal text-brand-600 mr-2"></i>Progress Log
                </h3>
                <div id="progress-log" class="bg-gray-50 p-4 rounded-lg border border-gray-200 font-mono text-sm h-48 overflow-y-auto"></div>
            </div>
        </div>

        <div id="results-container" class="hidden">
            <div class="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
                <h2 class="text-2xl font-heading font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-chart-pie text-brand-600 mr-3"></i>Summary Statistics
                </h2>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
                    <div class="bg-brand-50 rounded-xl p-4 text-center shadow-sm border border-brand-100">
                        <h3 class="text-sm font-medium text-brand-800 mb-1">Total Products</h3>
                        <p id="total-products" class="text-2xl font-bold text-brand-600">0</p>
                    </div>
                    <div class="bg-green-50 rounded-xl p-4 text-center shadow-sm border border-green-100">
                        <h3 class="text-sm font-medium text-green-800 mb-1">Successful Scrapers</h3>
                        <p id="successful-scrapers" class="text-2xl font-bold text-green-600">0</p>
                    </div>
                    <div class="bg-red-50 rounded-xl p-4 text-center shadow-sm border border-red-100">
                        <h3 class="text-sm font-medium text-red-800 mb-1">Failed Scrapers</h3>
                        <p id="failed-scrapers" class="text-2xl font-bold text-red-600">0</p>
                    </div>
                    <div class="bg-brand-50 rounded-xl p-4 text-center shadow-sm border border-brand-100">
                        <h3 class="text-sm font-medium text-brand-800 mb-1">Unique Brands</h3>
                        <p id="unique-brands" class="text-2xl font-bold text-brand-600">0</p>
                    </div>
                    <div class="bg-accent-50 rounded-xl p-4 text-center shadow-sm border border-accent-100">
                        <h3 class="text-sm font-medium text-accent-800 mb-1">Available Products</h3>
                        <p id="available-products" class="text-2xl font-bold text-accent-600">0</p>
                    </div>
                    <div class="bg-accent-50 rounded-xl p-4 text-center shadow-sm border border-accent-100">
                        <h3 class="text-sm font-medium text-accent-800 mb-1">Unavailable Products</h3>
                        <p id="unavailable-products" class="text-2xl font-bold text-accent-600">0</p>
                    </div>
                </div>

                <h3 class="text-lg font-heading font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-building text-brand-600 mr-2"></i>Products Per Supplier
                </h3>
                <div id="supplier-stats" class="flex flex-wrap gap-2 mb-4"></div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                <h2 class="text-2xl font-heading font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-search text-brand-600 mr-3"></i>Search Results
                </h2>
                <div class="flex flex-col md:flex-row gap-4 mb-6">
                    <div class="relative flex-grow">
                        <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                            <i class="fas fa-filter"></i>
                        </span>
                        <input type="text" id="filter-input" placeholder="Filter results..."
                               class="w-full pl-10 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-brand-500">
                    </div>
                    <select id="product-code-filter"
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-brand-500">
                        <option value="all">All Product Codes</option>
                    </select>
                    <select id="supplier-filter"
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-brand-500">
                        <option value="all">All Suppliers</option>
                    </select>
                    <select id="brand-filter"
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-brand-500">
                        <option value="all">All Brands</option>
                    </select>
                    <div class="flex items-center">
                        <label class="inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="show-brand-column" class="sr-only peer">
                            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-brand-500 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-500"></div>
                            <span class="ml-3 text-sm font-medium text-gray-700">Show Brand Column</span>
                        </label>
                    </div>
                </div>
                <div class="overflow-x-auto rounded-lg border border-gray-200">
                    <table id="results-table" class="min-w-full bg-white">
                        <thead class="bg-gray-50 border-b border-gray-200">
                            <tr>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Code / Brand</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                                <th id="brand-column-header" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden">Brand</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exchange Value</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Availability</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery</th>
                            </tr>
                        </thead>
                        <tbody id="results-body" class="divide-y divide-gray-200">
                            <!-- Results will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div id="error-container" class="hidden max-w-3xl mx-auto bg-red-50 border border-red-200 p-6 rounded-xl shadow-lg mb-8">
            <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                    <i class="fas fa-exclamation-circle text-red-500 text-xl"></i>
                </div>
                <div class="ml-4">
                    <h2 class="text-xl font-heading font-bold text-red-700 mb-2">Error</h2>
                    <p id="error-message" class="text-red-600"></p>
                </div>
            </div>
        </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="py-6 app-bar-gradient text-center text-white mt-auto">
        <div class="container mx-auto px-4">
            <div class="flex flex-col items-center">
                <img src="/logo.png" alt="Company Logo" class="h-8 bg-white p-1 rounded mb-3">
                <p class="text-sm mb-2">© 2023 Multi-Scraper Dashboard | All rights reserved</p>
                <p class="text-xs opacity-75">Unified product search across multiple suppliers</p>
            </div>
        </div>
    </footer>

    <!-- Floating Action Button for Error Logs -->
    <div id="error-logs-fab" class="fixed bottom-6 right-6 hidden z-50">
        <button id="show-error-logs-btn" class="bg-red-600 hover:bg-red-700 text-white rounded-full w-16 h-16 flex items-center justify-center shadow-lg transition-all transform hover:scale-105 focus:outline-none pulse-animation">
            <i class="fas fa-exclamation-triangle text-2xl"></i>
        </button>
        <span class="absolute -top-2 -right-2 bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full" id="error-count">0</span>
    </div>

    <!-- Error Logs Modal -->
    <div id="error-logs-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[80vh] flex flex-col">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center app-bar-gradient text-white rounded-t-xl">
                <h3 class="text-xl font-bold font-heading">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Scraper Error Logs
                </h3>
                <button id="close-error-logs-modal" class="text-white hover:text-gray-200 focus:outline-none">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="p-6 overflow-y-auto flex-grow">
                <div id="error-logs-container" class="space-y-4">
                    <!-- Error logs will be inserted here dynamically -->
                </div>
            </div>
            <div class="p-4 border-t border-gray-200 bg-gray-50 rounded-b-xl">
                <button id="close-error-logs-btn" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg transition-colors">
                    Close
                </button>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="script.js"></script>
</body>
</html>
