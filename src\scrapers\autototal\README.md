# Autototal Scraper

This scraper extracts product information from the Autototal supplier website.

## Architecture

The scraper has been refactored to use a modular architecture for better maintainability and code organization. The code is now split into several modules, each with a specific responsibility:

```
src/scrapers/autototal/
├── index.js                 # Original entry point (for backward compatibility)
├── orchestrator.js          # New entry point that orchestrates the scraping process
├── config.js                # Configuration, constants, and selectors
├── auth.js                  # Authentication-related functions
├── navigation.js            # Page navigation and pagination functions
├── extractors.js            # Data extraction functions
├── utils.js                 # Scraper-specific utility functions
├── supplier_names.js        # List of brand names for parsing
└── state/                   # State directory (unchanged)
    ├── cookies.json
    ├── session-url.txt
    └── profile/
```

## Usage

The scraper can be used in two ways:

### 1. Original (Legacy) Version

```javascript
const { runAutototalScraper } = require('./scrapers/autototal');

async function main() {
  const results = await runAutototalScraper('PRODUCT_CODE');
  console.log(results);
}

main();
```

### 2. Modular Version

```javascript
const { runAutototalScraperModular } = require('./scrapers/autototal');

async function main() {
  const results = await runAutototalScraperModular('PRODUCT_CODE');
  console.log(results);
}

main();
```

## Module Responsibilities

### config.js

Contains all configuration values, constants, and selectors used throughout the scraper. This makes it easy to update selectors or other configuration values in one place.

### auth.js

Contains functions related to authentication:
- `ensureLoggedIn(page)`: Checks if the user is logged in and performs login if needed
- `performLogin(page)`: Handles the login process

### navigation.js

Contains functions for navigating the website:
- `searchProduct(page, productCode)`: Searches for a product using the search form
- `hasPagination(page)`: Checks if the page has pagination
- `getCurrentPageNumber(page)`: Gets the current active page number
- `navigateToNextPage(page, visitedPages)`: Navigates to the next page with retry logic

### extractors.js

Contains functions for extracting data from the page:
- `waitForProductPrices(page)`: Waits for all products to have price information
- `extractProductsFromPage(page, brandNames)`: Extracts product data from the current page

### utils.js

Contains utility functions specific to this scraper:
- `formatProductForLog(product)`: Formats a product object for logging
- `summarizeResults(results)`: Summarizes the results of a scraping operation

## Future Development

When making changes to the scraper:

1. Update the appropriate module based on what you're changing:
   - Selectors or configuration values → `config.js`
   - Authentication logic → `auth.js`
   - Navigation or pagination logic → `navigation.js`
   - Data extraction logic → `extractors.js`
   - Utility functions → `utils.js`

2. Use the modular version (`runAutototalScraperModular`) for new code.

3. Keep the original version (`runAutototalScraper`) for backward compatibility.

## Error Handling

The scraper includes retry logic for navigation and pagination to handle cases where:
- Clicking page links fails to register
- Content doesn't change after clicking
- The website navigates back to the first page after visiting the last page

## Known Issues and Fixes

- **Fixed**: Pagination stopping after first page - Fixed an issue where the scraper was stopping after the first page due to duplicate checks for already visited pages in both orchestrator.js and navigation.js.

- **Fixed**: Infinite loop with small number of pages - Fixed an issue where the scraper would enter an infinite loop when there were only 2 pages of results. Added additional checks:
  1. Track how many times each page has been visited
  2. Stop if any page has been visited more than 3 times
  3. Detect when we've completed multiple cycles through all available pages

- **Fixed**: Last page detection - The scraper now detects when it has reached the last page and stops pagination:
  1. Added a new `isLastPage` function that compares the current page number with the total pages
  2. Stops pagination immediately when the last page is reached
  3. Prevents unnecessary navigation back to page 1 via the Next button
  4. Displays a clear message when the last page is reached

- **Handled**: Clicking the next button on the final page takes the user back to the first page - The scraper detects this behavior and handles it appropriately

- **Improved**: Clicking page links sometimes fails to register on the website - Added retry logic with multiple click methods

- **Fixed**: Delay between search results and processing - The scraper now checks for pagination without unnecessary waiting:
  1. First checks if pagination already exists without waiting
  2. Only waits a short time (1 second max) if pagination isn't immediately found
  3. Eliminates the long delay between "Search results loaded" and "Processing page" logs
  4. Makes the scraper much more responsive for single-page results

- **Updated**: Returnable field logic - The scraper now correctly determines if a product is returnable:
  1. Sets returnable to false when delivery contains 'express' (case insensitive)
  2. Sets returnable to false when delivery equals 'Verifica disponibilitatea'
  3. Sets returnable to false when delivery starts with 'Termen mediu' (any delivery with this prefix)
  4. Sets returnable to true for all other delivery values

- **Improved**: Streamlined logging format - Completely redesigned the logging to be concise and informative:
  1. Changed page processing logs to use "[current/total]" format (e.g., "Processing page [2/5]")
  2. Removed all non-essential logs (pagination checks, link counts, navigation attempts)
  3. Only show important information like product counts and errors
  4. Eliminated redundant navigation and click logs
  5. Reduced error verbosity to focus on critical issues
  6. Added results JSON logging at the end of the script
  7. Added browser closing confirmation
