/**
 * Utility functions specific to the Autototal scraper
 */

/**
 * Formats a product object for logging
 * @param {Object} product - Product object
 * @returns {string} - Formatted string representation
 */
function formatProductForLog(product) {
  return `${product.brand} ${product.productCode} - ${product.name} - ${product.retailPrice}`;
}

/**
 * Summarizes the results of a scraping operation
 * @param {Array<Object>} results - Array of product objects
 * @returns {Object} - Summary object with statistics
 */
function summarizeResults(results) {
  if (!results || results.length === 0) {
    return {
      count: 0,
      brands: [],
      priceRanges: { min: 0, max: 0, avg: 0 },
      requestPrices: 0
    };
  }

  // Count unique brands
  const brands = [...new Set(results.map(r => r.brand))];
  
  // Calculate price statistics
  const prices = results
    .map(r => typeof r.retailPrice === 'number' ? r.retailPrice : null)
    .filter(p => p !== null);
  
  const requestPrices = results.filter(r => r.retailPrice === 'Available via request').length;
  
  const priceRanges = {
    min: prices.length ? Math.min(...prices) : 0,
    max: prices.length ? Math.max(...prices) : 0,
    avg: prices.length ? prices.reduce((sum, p) => sum + p, 0) / prices.length : 0
  };

  return {
    count: results.length,
    brands,
    priceRanges,
    requestPrices
  };
}

module.exports = {
  formatProductForLog,
  summarizeResults
};
