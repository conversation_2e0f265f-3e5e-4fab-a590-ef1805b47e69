# Scraper Refactoring Plan

This document outlines the plan for restructuring the scraper project to support multiple scrapers, isolate state, promote code reuse, and prepare for a potential future API.

## Proposed Directory Structure

```
autonet-scrapper/
├── .env                   # Global environment variables (credentials, API keys)
├── package.json           # Project dependencies and scripts
├── package-lock.json      # Lockfile for dependencies
├── node_modules/          # Installed Node.js packages
├── reports/               # (Optional) Directory for scraper output files
├── src/                   # Main source code directory
│   ├── index.js           # Main application entry point / Future API server
│   ├── shared/            # Reusable code across scrapers
│   │   ├── browser.js     # Puppeteer browser launch/config logic
│   │   ├── session.js     # Generic cookie/session handling
│   │   └── utils.js       # Common utility functions (delays, etc.)
│   └── scrapers/          # Directory containing individual scrapers
│       ├── autonet/         # Scraper for Autonet supplier
│       │   ├── index.js     # Core Autonet scraping logic
│       │   ├── config.js    # Autonet-specific config (URLs, selectors)
│       │   └── state/       # Autonet-specific state files
│       │       ├── cookies.json
│       │       ├── session-url.txt
│       │       └── profile/   # Puppeteer user profile for Autonet
│       └── another_supplier/ # Structure for another scraper
│           ├── index.js
│           ├── config.js
│           └── state/
│               ├── cookies.json
│               ├── session-url.txt
│               └── profile/
└── REFACTORING_PLAN.md    # This plan file
```

## Explanation

1.  **Root:** Contains global config (`.env`), Node.js project files (`package.json`, etc.), the main `src/` directory, and potentially an output `reports/` directory.
2.  **`src/`**:
    *   **`index.js`**: The primary entry point. Initially, it might run specific scrapers, but it's positioned to evolve into an API server (e.g., using Express).
    *   **`shared/`**: Centralizes reusable logic:
        *   `browser.js`: Handles Puppeteer setup (launch, stealth, user-agent).
        *   `session.js`: Provides generic functions for managing cookies and session state (like URLs), likely accepting paths as arguments.
        *   `utils.js`: Contains common helper functions (`randomDelay`, `sleep`).
    *   **`scrapers/`**: The core directory holding individual scraper modules.
        *   Each subdirectory (e.g., `autonet/`, `another_supplier/`) represents one scraper.
        *   Inside each scraper directory:
            *   `index.js`: Contains the main scraping workflow for that specific supplier.
            *   `config.js` (Recommended): Stores supplier-specific settings (URLs, CSS selectors) to keep the main logic clean.
            *   `state/`: A dedicated folder isolates the scraper's persistent state (cookies, session tokens/URLs, Puppeteer profile data). This prevents interference between different scrapers.
3.  **`reports/`** (Optional): A designated location for storing generated reports or data files from the scrapers.

## Benefits

*   **Modularity:** Each scraper's logic, configuration, and state are grouped together.
*   **Reusability:** Common browser and utility functions are shared, reducing duplication.
*   **State Isolation:** Separating state files (cookies, profiles) per scraper prevents conflicts and allows scrapers to run independently or concurrently without interfering with each other's sessions.
*   **Configuration Management:** Clear separation between global (`.env`) and scraper-specific (`config.js`) settings.
*   **Scalability:** Adding a new scraper involves creating a new directory under `src/scrapers/`, implementing its logic, and potentially reusing shared components.
*   **API Ready:** The structure, with a central `src/index.js` and modular scrapers, is well-suited for building an API wrapper.