const { sleep, randomDelay } = require('../../shared/utils');
const config = require('./config');

/**
 * Searches for a product using the search form
 * @param {Object} page - Puppeteer page object
 * @param {string} productCode - Product code to search for
 * @returns {Promise<void>}
 */
async function searchProduct(page, productCode) {
  console.log(`🔍 Searching for product code: ${productCode}`);

  // Wait for search input and clear it
  await page.waitForSelector(config.SELECTORS.SEARCH.INPUT);
  await page.evaluate(
    sel => document.querySelector(sel).value = '',
    config.SELECTORS.SEARCH.INPUT
  );

  // Type product code
  await page.type(
    config.SELECTORS.SEARCH.INPUT,
    productCode,
    { delay: randomDelay() }
  );
  await sleep(randomDelay(config.DELAYS.BETWEEN_INPUTS[0], config.DELAYS.BETWEEN_INPUTS[1]));

  // Click search button
  await page.click(config.SELECTORS.SEARCH.BUTTON);

  // Wait for results
  console.log('⏳ Waiting for search results to load...');
  try {
    await page.waitForSelector(config.SELECTORS.SEARCH.RESULTS_TABLE, {
      visible: true,
      timeout: config.SEARCH_RESULTS_TIMEOUT
    });
    await sleep(randomDelay(config.DELAYS.BETWEEN_INPUTS[0], config.DELAYS.BETWEEN_INPUTS[1]));
    console.log('✅ Search results loaded successfully');
  } catch (error) {
    console.log('⚠️ Timeout waiting for search results, will try to continue anyway');
  }
}

/**
 * Checks if the page has pagination
 * @param {Object} page - Puppeteer page object
 * @returns {Promise<boolean>} - True if pagination exists
 */
async function hasPagination(page) {
  // First check if pagination already exists without waiting
  const paginationExists = await page.evaluate(() => {
    const pag = document.querySelector('ul.pagination');
    return pag && getComputedStyle(pag).display !== 'none';
  });

  if (paginationExists) {
    return true;
  }

  // If not immediately found, wait a short time (1 second max) to see if it appears
  try {
    await page.waitForSelector(config.SELECTORS.PAGINATION.CONTAINER, {
      visible: true,
      timeout: 1000 // Short timeout since we already checked once
    });

    return await page.evaluate(() => {
      const pag = document.querySelector('ul.pagination');
      return pag && getComputedStyle(pag).display !== 'none';
    });
  } catch (e) {
    // No pagination found after short wait
    return false;
  }
}

/**
 * Gets the current active page number
 * @param {Object} page - Puppeteer page object
 * @returns {Promise<string|null>} - Current page number or null
 */
async function getCurrentPageNumber(page) {
  return await page.evaluate(() => {
    const active = document.querySelector('ul.pagination li.page-item.active');
    return active ? active.textContent.trim() : null;
  });
}

/**
 * Gets the total number of pages
 * @param {Object} page - Puppeteer page object
 * @returns {Promise<number>} - Total number of pages or 1 if not found
 */
async function getTotalPages(page) {
  return await page.evaluate(() => {
    const paginationItems = document.querySelectorAll('ul.pagination li.page-item:not(:first-child):not(:last-child)');
    if (paginationItems.length === 0) return 1;

    // Get all page numbers
    const pageNumbers = Array.from(paginationItems)
      .map(item => {
        const num = parseInt(item.textContent.trim());
        return isNaN(num) ? 0 : num;
      })
      .filter(num => num > 0);

    // Return the highest page number
    return pageNumbers.length > 0 ? Math.max(...pageNumbers) : 1;
  });
}

/**
 * Checks if the current page is the last page
 * @param {Object} page - Puppeteer page object
 * @returns {Promise<boolean>} - True if this is the last page
 */
async function isLastPage(page) {
  const currentPage = await getCurrentPageNumber(page);
  const totalPages = await getTotalPages(page);

  if (!currentPage) return false;

  // Check if current page is the last page
  return parseInt(currentPage) === totalPages;
}

/**
 * Navigates to the next page
 * @param {Object} page - Puppeteer page object
 * @param {Set} visitedPages - Set of already visited page numbers
 * @returns {Promise<boolean>} - True if navigation was successful
 */
async function navigateToNextPage(page, visitedPages) {
  // Ensure pagination is fully visible by scrolling to it
  await page.evaluate(() => {
    const paginationElement = document.querySelector('ul.pagination');
    if (paginationElement) {
      paginationElement.scrollIntoView({ block: 'center' });
      paginationElement.style.outline = '2px solid rgba(0, 123, 255, 0.5)';
    }
  });

  // Minimal delay after scrolling
  await sleep(config.DELAYS.MINIMAL_DELAY);

  // Get current active page
  const activePage = await getCurrentPageNumber(page);

  // Note: We're not checking for return to page 1 here anymore
  // That check is now done in the orchestrator.js file before calling this function

  // We don't need to check for already visited pages here
  // That's handled in the orchestrator.js file
  console.log(`📄 Navigating from page ${activePage}`);

  // Get the current content to detect changes after navigation
  const prevHtml = await page.$eval(config.SELECTORS.SEARCH.RESULTS_TABLE, el => el.innerHTML);

  // Try to find an unvisited page link
  return await tryNavigateToUnvisitedPage(page, visitedPages, prevHtml) ||
         await tryUseNextButton(page, visitedPages, prevHtml);
}

/**
 * Tries to navigate to an unvisited page using numbered page links
 * @param {Object} page - Puppeteer page object
 * @param {Set} visitedPages - Set of already visited page numbers
 * @param {string} prevHtml - Previous page HTML content
 * @returns {Promise<boolean>} - True if navigation was successful
 */
async function tryNavigateToUnvisitedPage(page, visitedPages, prevHtml) {
  // Make pagination links clickable
  await page.evaluate(() => {
    const pagination = document.querySelector('ul.pagination');
    if (pagination) {
      pagination.style.pointerEvents = 'auto';
      const links = pagination.querySelectorAll('a.page-link');
      for (const link of links) {
        link.style.pointerEvents = 'auto';
        link.style.cursor = 'pointer';
      }
    }
  });

  // Get all page links that aren't the active page, first, or last items
  const pageLinks = await page.$$('ul.pagination li.page-item:not(.active):not(:first-child):not(:last-child) a.page-link');

  // Find an unvisited page link
  let unvisitedLinks = [];

  for (const link of pageLinks) {
    const isAttached = await page.evaluate(el => !!el.isConnected, link);
    if (!isAttached) continue;

    const text = await page.evaluate(el => el.textContent.trim(), link);

    if (!visitedPages.has(text)) {
      unvisitedLinks.push({ link, text });
    }
  }

  if (unvisitedLinks.length > 0) {
    // Navigate to the first unvisited page without verbose logging
    return await attemptNavigation(page, unvisitedLinks[0].link, unvisitedLinks[0].text, prevHtml);
  }

  return false;
}

/**
 * Tries to use the "Next" button for navigation
 * @param {Object} page - Puppeteer page object
 * @param {Set} visitedPages - Set of already visited page numbers
 * @param {string} prevHtml - Previous page HTML content
 * @returns {Promise<boolean>} - True if navigation was successful
 */
async function tryUseNextButton(page, visitedPages, prevHtml) {
  // Try using the Next button with minimal logging
  try {
    // Find the "Next" button
    const nextButton = await page.$(config.SELECTORS.PAGINATION.NEXT);
    if (!nextButton) return false;

    // Check if Next button is disabled
    const isNextDisabled = await page.evaluate(btn => {
      const parentLi = btn.closest('li.page-item');
      return parentLi.classList.contains('disabled');
    }, nextButton);

    if (isNextDisabled) return false;

    // Get current page before clicking
    const beforePage = await getCurrentPageNumber(page);

    // Attempt navigation with the Next button
    const success = await attemptNavigation(page, nextButton, 'Next', prevHtml);

    if (success) {
      // Verify we moved to a different page
      const afterPage = await getCurrentPageNumber(page);
      if (afterPage && afterPage !== beforePage) {
        // No need to log successful navigation, it will be logged in the orchestrator
        visitedPages.add(afterPage);
        return true;
      }
    }

    return false;
  } catch (error) {
    return false;
  }
}

/**
 * Attempts to navigate to a page with retry logic
 * @param {Object} page - Puppeteer page object
 * @param {Object} linkElement - Puppeteer element handle for the link
 * @param {string} linkText - Text of the link for logging
 * @param {string} prevHtml - Previous page HTML content
 * @returns {Promise<boolean>} - True if navigation was successful
 */
async function attemptNavigation(page, linkElement, linkText, prevHtml) {
  // Retry logic with minimal logging
  let retryCount = 0;

  while (retryCount < config.MAX_RETRIES) {
    try {
      if (retryCount > 0) {
        // Only log retries, not first attempts
        console.log(`🔄 Retry navigation attempt ${retryCount}`);
        await sleep(randomDelay(config.DELAYS.RETRY_DELAY[0], config.DELAYS.RETRY_DELAY[1]));
      }

      // Scroll to make the link visible
      await page.evaluate(linkElement => {
        linkElement.scrollIntoView({ block: 'nearest' });
      }, linkElement);

      await sleep(config.DELAYS.MINIMAL_DELAY);

      // Try standard click
      try {
        await linkElement.click();
      } catch (clickError) {
        // Try JavaScript click as fallback without verbose logging
        try {
          await page.evaluate(el => {
            // Simulate a more natural click with mouse events
            const mouseDown = new MouseEvent('mousedown', {
              bubbles: true,
              cancelable: true,
              view: window
            });

            const mouseUp = new MouseEvent('mouseup', {
              bubbles: true,
              cancelable: true,
              view: window
            });

            const click = new MouseEvent('click', {
              bubbles: true,
              cancelable: true,
              view: window
            });

            // Dispatch the events in sequence
            el.dispatchEvent(mouseDown);
            el.dispatchEvent(mouseUp);
            el.dispatchEvent(click);
          }, linkElement);
        } catch (jsClickError) {
          throw new Error('All click methods failed');
        }
      }

      // Wait for content to change
      await page.waitForFunction(
        (sel, prev) => document.querySelector(sel)?.innerHTML !== prev,
        { timeout: config.CONTENT_CHANGE_TIMEOUT },
        config.SELECTORS.SEARCH.RESULTS_TABLE,
        prevHtml
      );

      await sleep(randomDelay(config.DELAYS.AFTER_NAVIGATION[0], config.DELAYS.AFTER_NAVIGATION[1]));
      return true;
    } catch (error) {
      // Only log the retry count, not the detailed error
      retryCount++;
    }
  }

  // Simple failure message after all retries
  console.log(`❌ Navigation failed after ${config.MAX_RETRIES} attempts`);
  return false;
}

module.exports = {
  searchProduct,
  hasPagination,
  getCurrentPageNumber,
  getTotalPages,
  isLastPage,
  navigateToNextPage
};
