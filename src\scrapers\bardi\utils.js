/**
 * Utility functions for the Bardi scraper
 */

/**
 * Attempts to normalize a Bardi product code by extracting the actual product code
 * from the brand-prefixed code that <PERSON><PERSON> uses.
 *
 * @param {string} bardiCode - The product code from Bardi (typically prefixed with brand identifier)
 * @param {string} searchedCode - The original product code that was searched for
 * @returns {object} - Object containing the original code and the normalized code
 */
function normalizeBardiProductCode(bardiCode, searchedCode) {
  if (!bardiCode) {
    return {
      originalCode: null,
      normalizedCode: null
    };
  }

  // If the bardiCode is exactly the same as the searched code, return it as is
  if (bardiCode === searchedCode) {
    return {
      originalCode: bardiCode,
      normalizedCode: bardiCode
    };
  }

  // Check if the bardiCode contains the searchedCode as a substring
  // This is a direct match case - the bardiCode has a prefix but the rest matches exactly
  if (bardiCode.includes(searchedCode)) {
    return {
      originalCode: bardiCode,
      normalizedCode: searchedCode
    };
  }

  // Handle case where searchedCode might have dots, dashes, or spaces that are removed in bardiCode
  // For example, searchedCode = "12.50003" and bardiCode = "*********"
  // Or searchedCode = "0 986 424 797" and bardiCode = "0986424797"
  const cleanSearchedCode = searchedCode.replace(/[.\- ]/g, '');

  // First check if bardiCode is exactly the same as searchedCode but without spaces/dots/dashes
  // This is the most common case for codes like "0 986 424 797" -> "0986424797"
  // Use case-insensitive comparison to handle potential case differences

  // Clean the Bardi code as well to handle any dots/dashes it might have
  const cleanBardiCode = bardiCode.replace(/[.\- ]/g, '');

  // Compare the cleaned versions of both codes (case-insensitive)
  if (cleanSearchedCode.toLowerCase() === cleanBardiCode.toLowerCase()) {
    console.log(`MATCH FOUND: Cleaned codes match exactly (case-insensitive)`);
    console.log(`  Cleaned search code: "${cleanSearchedCode}"`);
    console.log(`  Cleaned Bardi code: "${cleanBardiCode}"`);
    return {
      originalCode: bardiCode,
      normalizedCode: searchedCode
    };
  }

  // Then check if bardiCode includes the cleaned searched code (for prefix cases)
  // Use case-insensitive check by converting both to lowercase
  const bardiCodeLower = bardiCode.toLowerCase();
  const cleanBardiCodeLower = cleanBardiCode.toLowerCase();
  const cleanSearchedCodeLower = cleanSearchedCode.toLowerCase();

  if (bardiCodeLower.includes(cleanSearchedCodeLower) || cleanBardiCodeLower.includes(cleanSearchedCodeLower)) {
    console.log(`MATCH FOUND: Bardi code contains the cleaned search code (case-insensitive)`);
    return {
      originalCode: bardiCode,
      normalizedCode: searchedCode // Return the original searched code with dots/dashes/spaces
    };
  }

  // Check if the bardiCode contains the cleaned searched code with a prefix
  // For example, searchedCode = "0 986 424 797" and bardiCode = "BOSCH0986424797"
  // Use case-insensitive check
  if ((bardiCodeLower.endsWith(cleanSearchedCodeLower) && bardiCode.length > cleanSearchedCode.length) ||
      (cleanBardiCodeLower.endsWith(cleanSearchedCodeLower) && cleanBardiCode.length > cleanSearchedCode.length)) {
    console.log(`MATCH FOUND: Bardi code ends with the cleaned search code (case-insensitive)`);
    return {
      originalCode: bardiCode,
      normalizedCode: searchedCode
    };
  }

  // Check if the bardiCode contains the cleaned searched code anywhere
  // This handles cases where the brand identifier might be in the middle
  // For example, searchedCode = "ABC123" and bardiCode = "ABCBRANDID123"
  if (cleanSearchedCode.length >= 5) { // Only try this for codes of reasonable length
    const firstPart = cleanSearchedCode.substring(0, Math.ceil(cleanSearchedCode.length / 2));
    const secondPart = cleanSearchedCode.substring(Math.ceil(cleanSearchedCode.length / 2));

    // Use case-insensitive comparison
    const firstPartLower = firstPart.toLowerCase();
    const secondPartLower = secondPart.toLowerCase();

    // Check both original and cleaned Bardi codes
    const checkMiddleInsertion = (codeToCheck) => {
      return codeToCheck.includes(firstPartLower) &&
             codeToCheck.includes(secondPartLower) &&
             codeToCheck.indexOf(firstPartLower) < codeToCheck.indexOf(secondPartLower);
    };

    // If both the first and second parts of the cleaned code are in the bardiCode,
    // and they're in the right order, it's likely a match with something inserted in the middle
    if (checkMiddleInsertion(bardiCodeLower) || checkMiddleInsertion(cleanBardiCodeLower)) {
      console.log(`MATCH FOUND: Bardi code contains both parts of the split search code in correct order`);
      return {
        originalCode: bardiCode,
        normalizedCode: searchedCode
      };
    }
  }

  // More complex case: Try to find the longest common substring
  // This helps with cases where the code might have slight variations
  // For example, searchedCode = "GDB1550DTE" and bardiCode = "TRWGDB1550DTE"

  // Try all combinations of original/cleaned codes
  const substrings = [
    // Original codes
    findLongestCommonSubstring(bardiCode, searchedCode),
    // Cleaned codes
    findLongestCommonSubstring(cleanBardiCode, cleanSearchedCode),
    // Mixed (original and cleaned)
    findLongestCommonSubstring(bardiCode, cleanSearchedCode),
    findLongestCommonSubstring(cleanBardiCode, searchedCode)
  ];

  // Find the longest substring
  const bestCommonSubstring = substrings.reduce((longest, current) =>
    current.length > longest.length ? current : longest, "");

  // If we found a substantial common substring (at least 60% of the searched code length)
  const threshold = Math.min(searchedCode.length, cleanSearchedCode.length) * 0.6;
  if (bestCommonSubstring && bestCommonSubstring.length >= threshold) {
    console.log(`MATCH FOUND: Longest common substring "${bestCommonSubstring}" exceeds threshold (${bestCommonSubstring.length} >= ${threshold})`);
    return {
      originalCode: bardiCode,
      normalizedCode: searchedCode
    };
  }

  // If we can't confidently normalize the code, return the original
  return {
    originalCode: bardiCode,
    normalizedCode: bardiCode
  };
}

/**
 * Finds the longest common substring between two strings
 *
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {string} - The longest common substring
 */
function findLongestCommonSubstring(str1, str2) {
  if (!str1 || !str2) {
    return '';
  }

  const m = str1.length;
  const n = str2.length;
  let maxLength = 0;
  let endIndex = 0;

  // Create a table to store lengths of longest common suffixes
  const dp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));

  // Fill the dp table
  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1] + 1;

        if (dp[i][j] > maxLength) {
          maxLength = dp[i][j];
          endIndex = i - 1;
        }
      }
    }
  }

  // If no common substring was found
  if (maxLength === 0) {
    return '';
  }

  // Extract the longest common substring
  return str1.substring(endIndex - maxLength + 1, endIndex + 1);
}

module.exports = {
  normalizeBardiProductCode
};
