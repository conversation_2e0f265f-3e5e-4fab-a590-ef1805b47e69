const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });

const { launchBrowser } = require('../../shared/browser');
const { loadCookiesFromFile, loadSessionUrlFromFile } = require('../../shared/session');
const { sleep, randomDelay } = require('../../shared/utils');
const { brandNames } = require('./supplier_names');

// Import our modular components
const config = require('./config');
const auth = require('./auth');
const navigation = require('./navigation');
const extractors = require('./extractors');
const utils = require('./utils');

/**
 * Main function to run the Autototal scraper using the modular architecture
 * @param {string} productCode - Product code to search for
 * @returns {Promise<Array<Object>|null>} - Array of product objects or null on error
 */
async function runAutototalScraperModular(productCode) {
  console.log(`🚀 Starting ${config.SCRAPER_NAME} scraper (modular version) for code: ${productCode}`);
  let browser;

  try {
    // Initialize browser
    const { browser: launchedBrowser, page } = await launchBrowser({
      userDataDir: config.PROFILE_PATH
    });
    browser = launchedBrowser;

    // Load session and navigate to test page
    let testPage = loadSessionUrlFromFile(config.SESSION_URL_PATH) || config.DEFAULT_TEST_PAGE;
    await loadCookiesFromFile(page, config.COOKIES_PATH);
    await page.goto(testPage, {
      waitUntil: 'domcontentloaded',
      timeout: config.NAVIGATION_TIMEOUT
    });
    await sleep(randomDelay(
      config.DELAYS.AFTER_PAGE_LOAD[0],
      config.DELAYS.AFTER_PAGE_LOAD[1]
    ));

    // Ensure logged in
    await auth.ensureLoggedIn(page);

    // Search for product
    await navigation.searchProduct(page, productCode);
    await sleep(randomDelay(1000, 1200));
    // Check for pagination
    const hasPagination = await navigation.hasPagination(page);

    // Collect results
    const results = [];
    const visitedPages = new Set();
    const pageVisitCounts = {}; // Track how many times we've visited each page

    // Get total pages
    let totalPages = 1;
    if (hasPagination) {
      totalPages = await navigation.getTotalPages(page);
      console.log(`📚 Found ${totalPages} total pages`);
    }

    if (!hasPagination && visitedPages.size === 0) {
      console.log('� Processing page [1/1]');

      // Single page scraping
      const pageData = await extractors.extractProductsFromPage(page, brandNames);
      console.log(`📊 Found ${pageData.length} products on this page`);
      results.push(...pageData);
    } else {
      // Multi-page scraping with pagination
      let pageVisitCount = 0;

      while (pageVisitCount < config.MAX_PAGES_TO_VISIT) {
        pageVisitCount++;

        // Wait for pagination to be visible
        try {
          await page.waitForSelector(config.SELECTORS.PAGINATION.CONTAINER, {
            visible: true,
            timeout: 10000
          });
        } catch (error) {
          console.log('⚠️ Pagination not visible, might be on the last page');
          break;
        }

        // Get current active page
        const currentPage = await navigation.getCurrentPageNumber(page);

        // Check if we're back on page 1 after having visited other pages
        if (currentPage === '1' && visitedPages.size > 0 && visitedPages.has('1')) {
          console.log('⚠️ Detected navigation back to page 1 after visiting other pages');
          console.log('🔄 This might indicate we\'ve completed a full cycle through all pages');

          // Check if we've visited a significant number of pages before deciding to stop
          const highestPageVisited = Math.max(...Array.from(visitedPages).map(p => parseInt(p) || 0));
          console.log(`ℹ️ Highest page number visited: ${highestPageVisited}`);

          // Count how many times we've returned to page 1
          pageVisitCount = pageVisitCount || 0;
          const returnToPage1Count = pageVisitCount > 1 ? Math.floor(pageVisitCount / highestPageVisited) : 0;

          // If we've returned to page 1 more than once, or if we've visited all available pages
          if (returnToPage1Count > 1 || highestPageVisited >= 2) {
            console.log(`✅ Completed pagination cycle (visited ${visitedPages.size} pages, highest: ${highestPageVisited})`);
            break;
          } else {
            console.log('ℹ️ Continuing for one more cycle to ensure we visit all pages');
          }
        }

        // Process the current page with new format [current/total]
        console.log(`📄 Processing page [${currentPage}/${totalPages}]`);

        // Track how many times we've visited this page
        if (currentPage) {
          pageVisitCounts[currentPage] = (pageVisitCounts[currentPage] || 0) + 1;

          // If we've visited this page too many times, break to prevent infinite loop
          if (pageVisitCounts[currentPage] > 3) {
            console.log(`⚠️ Stopping: visited page [${currentPage}/${totalPages}] too many times`);
            break;
          }

          // Mark the current page as visited AFTER processing it
          visitedPages.add(currentPage);
        }

        // Extract data from current page
        const pageData = await extractors.extractProductsFromPage(page, brandNames);
        console.log(`📊 Found ${pageData.length} products on this page`);
        results.push(...pageData);

        // Check if we're on the last page
        const onLastPage = await navigation.isLastPage(page);
        if (onLastPage) {
          console.log(`🏁 Reached the last page (${currentPage}/${totalPages}). Stopping pagination.`);
          break;
        }

        // Navigate to next page
        const hasMorePages = await navigation.navigateToNextPage(page, visitedPages);
        if (!hasMorePages) {
          console.log('🛑 No more pages to visit');
          break;
        }
      }

      // Check if we hit the maximum page limit
      if (pageVisitCount >= config.MAX_PAGES_TO_VISIT) {
        console.log(`⚠️ Reached maximum page visit limit (${config.MAX_PAGES_TO_VISIT}). Stopping pagination to prevent infinite loop.`);
      }
    }

    // Summarize and log results
    const summary = utils.summarizeResults(results);
    console.log(`📊 Scraped ${results.length} products.`);
    console.log(`📊 Found ${summary.brands.length} brands with prices ranging from ${summary.priceRanges.min} to ${summary.priceRanges.max}`);

    // Output full results for debugging
    // if (results.length > 0) {
    //   console.log('Sample product:');
    //   console.log(utils.formatProductForLog(results[0]));
    // }

    console.log(`✅ ${config.SCRAPER_NAME} scraper (modular version) finished successfully.`);

    //Close the browser before returning
    if (browser) {
      console.log('🔒 Closing browser...');
      await browser.close();
    }

    return results;

  } catch (err) {
    console.error(`❌ Error in ${config.SCRAPER_NAME} (modular version):`, err);
    if (browser) await browser.close();
    return null;
  }
}

module.exports = {
  runAutototalScraperModular,
};
