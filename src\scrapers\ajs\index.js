const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });

const { launchBrowser } = require('../../shared/browser');
const {
  saveCookiesToFile,
  loadCookiesFromFile,
  saveSessionUrlToFile,
  loadSessionUrlFromFile,
} = require('../../shared/session');
const { randomDelay, sleep } = require('../../shared/utils');

const SCRAPER_NAME = 'AJS';
const STATE_DIR = path.resolve(__dirname, 'state');
const COOKIES_PATH = path.join(STATE_DIR, 'cookies.json');
const SESSION_URL_PATH = path.join(STATE_DIR, 'session-url.txt');
const PROFILE_PATH = path.join(STATE_DIR, 'profile');

const LOGIN_URL = 'https://www.ajsparts.pl';
const DEFAULT_TEST_PAGE = 'https://www.ajsparts.pl';


const LOGGED_IN_SELECTOR = '#ctl00_lnkLogout';
const USERNAME_SELECTOR = '#ctl00_box_6_tbUserId';
const PASSWORD_SELECTOR = '#ctl00_box_6_tbPassword';

const SEARCH_INPUT_ID = '#ctl00_box_5_tbQuickSearch';
const SEARCH_BUTTON_POSTBACK = 'ctl00$box_5$btnQuickSearch'; // value passed to __doPostBack


const RESULTS_SECTION_SELECTOR = 'section.grow.w-full';
const RESULT_ITEMS_SELECTOR = 'ul.divide-y > li';

async function runAjsScraper(productCode) {
  console.log(`🚀 Starting ${SCRAPER_NAME} scraper for code: ${productCode}`);
  let browser;
  try {
    const { browser: launchedBrowser, page } = await launchBrowser({ userDataDir: PROFILE_PATH });
    browser = launchedBrowser;

    let testPage = loadSessionUrlFromFile(SESSION_URL_PATH) || DEFAULT_TEST_PAGE;
    await loadCookiesFromFile(page, COOKIES_PATH);
    await page.goto(testPage, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await sleep(1500);

    console.log('⏳ Attempting to click Accept All inside open Shadow DOM...');

    const clicked = await page.evaluate(() => {
      try {
        const host = document.querySelector('#usercentrics-cmp-ui');
        if (!host || !host.shadowRoot) {
          console.warn('⚠️ Shadow root not found.');
          return false;
        }

        const acceptButton = host.shadowRoot.querySelector('button#accept');
        if (acceptButton) {
          acceptButton.click();
          console.info('✅ Consent button clicked inside shadow DOM.');
          return true;
        }

        console.warn('❌ Accept button not found inside shadow DOM.');
        return false;
      } catch (err) {
        console.error('🔥 Error evaluating consent logic:', err);
        return false;
      }
    });

    if (clicked) {
      await sleep(randomDelay(1000, 1500)); // Optional delay for UI to settle
    }

    page.frames().forEach(f => console.log('🧩 Frame URL:', f.url()));
    try {
      await page.waitForSelector(LOGGED_IN_SELECTOR, { timeout: 1000 });
      console.log('✅ Already logged in. Continuing...');
    } catch {
      console.log('🔐 Not logged in or session expired. Performing login...');
      const pageHTML = await page.content();
      console.log('🔍 Checking for login field presence:', pageHTML.includes('ctl00_box_6_tbUserId'));

      const isOnLoginPage = await page.$(USERNAME_SELECTOR) !== null;

      if (!isOnLoginPage) {
        console.log(`Navigating to login page: ${LOGIN_URL}`);
        await page.goto(LOGIN_URL, { waitUntil: 'domcontentloaded', timeout: 6000 });
      } else {
        console.log('🔄 Already on the login page.');
      }

      await page.waitForSelector(USERNAME_SELECTOR);
      await page.type(USERNAME_SELECTOR, process.env.AJS_USER, { delay: randomDelay() });
      await sleep(randomDelay(300, 600));
      await page.type(PASSWORD_SELECTOR, process.env.AJS_PASSWORD, { delay: randomDelay() });
      await sleep(randomDelay(300, 600));
      await page.evaluate(() => {
        // ASP.NET WebForms login trigger
        __doPostBack('ctl00$box_6$btnLogin', '');
      });
      await new Promise(resolve => setTimeout(resolve, 1500)); // Wait for postback start
      await saveCookiesToFile(page, COOKIES_PATH);
      saveSessionUrlToFile(page.url(), SESSION_URL_PATH);
    }
    
    
    console.log(`🔎 Searching for product code: ${productCode}`);
    await page.waitForSelector(SEARCH_INPUT_ID);

    // Type the product code
    await page.evaluate((sel) => {
      const input = document.querySelector(sel);
      if (input) input.value = '';
    }, SEARCH_INPUT_ID);
    await page.type(SEARCH_INPUT_ID, productCode, { delay: 80 });
    await sleep(500);

    // Fire full ASP.NET + JS search trigger
    await page.evaluate(() => {
      const proceed = window.quickSearchBox68?.('#ctl00_box_5_tbQuickSearch');
      if (proceed === false) return;
      __doPostBack('ctl00$box_5$btnQuickSearch', '');
    });

    await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 });
    console.log('🔍 Search submitted successfully.');


    const TOTAL_PAGES_SELECTOR = '#ctl00_pagecontext_ctl00_pager_plPager';
    const NEXT_PAGE_POSTBACK = 'ctl00$pagecontext$ctl00$pager$lbNext';

    let currentPage = 1;

    // Step 1: Detect total pages
    const totalPages = await page.evaluate((selector) => {
      const container = document.querySelector(selector);
      if (!container) return 1;
      const match = container.textContent.match(/of\s+(\d+)/i);
      return match ? parseInt(match[1]) : 1;
    }, TOTAL_PAGES_SELECTOR);

    console.log(`📄 Total pages detected: ${totalPages}`);

    while (currentPage < totalPages) {
      const nextUrl = await page.evaluate(() => {
        const btn = document.querySelector('#ctl00_pagecontext_ctl00_pager_lbNext');
        if (!btn) return null;
    
        const onclick = btn.getAttribute('onclick');
        const match = onclick && onclick.match(/window\.location\.href\s*=\s*'([^']+)'/);
        return match ? match[1] : null;
      });
    
      if (!nextUrl) {
        console.warn(`❌ Could not extract next page URL on page ${currentPage}`);
        break;
      }
    
      console.log(`➡️ Going to page ${currentPage + 1}: ${nextUrl}`);
      await page.goto(nextUrl, { waitUntil: 'networkidle2' });

      currentPage++;
    
      // Scrape this page
      // await scrapeCurrentResults(page);
    }
    // await page.waitForSelector(SEARCH_INPUT_SELECTOR, { timeout: 5000 });
    // await page.evaluate(sel => document.querySelector(sel).value = '', SEARCH_INPUT_SELECTOR); // clear any existing value
    // await page.type(SEARCH_INPUT_SELECTOR, productCode, { delay: randomDelay(50, 100) });
    // await sleep(randomDelay(300, 600));
    
    // // Click the button that contains the SVG icon (has to go up to button from SVG)
    // console.log('🔍 Looking for the search button inside .flex.flex-shrink-0...');
    // const searchButton = await page.$('div.flex.flex-shrink-0 > button:nth-of-type(2)');
    // if (!searchButton) {
    //   throw new Error('❌ Could not find the search button.');
    // }
    // console.log('🖱️ Clicking the search button...');
    // await searchButton.click();
    // console.log('🚀 Search submitted. Waiting for results...');
    // await sleep(2000);
    // await page.waitForSelector(RESULTS_SECTION_SELECTOR, { timeout: 15000 });
    // console.log('📦 Results section loaded.');


    // const maxWaitTime = 15000;
    // const pollInterval = 500;
    // const minExpectedResults = 2; // adjust this threshold if needed

    // let itemsCount = 0;
    // const start = Date.now();

    // while (Date.now() - start < maxWaitTime) {
    //   itemsCount = await page.evaluate((sel) => {
    //     return document.querySelectorAll(sel).length;
    //   }, RESULT_ITEMS_SELECTOR);

    //   if (itemsCount >= minExpectedResults) {
    //     break;
    //   }

    //   await sleep(pollInterval);
    // }

    // if (itemsCount < minExpectedResults) {
    //   console.warn(`⚠️ Only ${itemsCount} result item(s) detected after timeout.`);
    // } else {
    //   console.log(`🔍 Detected ${itemsCount} product items.`);
    // }


    // console.log('⏳ Waiting until all product items have finished loading delivery data...');

    // const maxWaitForNoSpinner = 25000;
    // const spinnerPollInterval = 400;

    // let allSpinnersGone = false;
    // const spinnerCheckStart = Date.now();

    // while (Date.now() - spinnerCheckStart < maxWaitForNoSpinner) {
    //   const spinnersRemaining = await page.evaluate((liSelector) => {
    //     return Array.from(document.querySelectorAll(liSelector)).filter(li => {
    //       return li.querySelector('div.flex.items-left.text-gray-400 svg.animate-spin');
    //     }).length;
    //   }, RESULT_ITEMS_SELECTOR);

    //   //console.log(`🌀 Still waiting... ${spinnersRemaining} item(s) with loading spinners.`);

    //   if (spinnersRemaining === 0) {
    //     allSpinnersGone = true;
    //     break;
    //   }

    //   await sleep(spinnerPollInterval);
    // }

    // if (allSpinnersGone) {
    //   console.log('✅ All product items finished loading delivery/prices.');
    // } else {
    //   console.warn('⚠️ Some products still had loading indicators. Proceeding anyway...');
    // }
   

    // const results = await page.evaluate((liSelector) => {
    //   const items = [];
    //   const listItems = document.querySelectorAll(liSelector);
    
    //   listItems.forEach(li => {
    //     // Skip items that still have the loading spinner
    //     if (li.querySelector('div.flex.items-left.text-gray-400 svg.animate-spin')) return;
    
    //     // Get price
    //     const priceEl = li.querySelector('div.text-sm.whitespace-nowrap.tabular-nums');
    //     const price = priceEl?.textContent.trim();
    //     if (!price) return; // Skip if no price
    
    //     // Get name
    //     const name = li.querySelector('div.text-left.text-xs.font-semibold')?.textContent.trim() || 'N/A';
    
    //     // Get brand and code
    //     const brand = li.querySelector('div.flex.flex-col.sm\\:flex-row button')?.textContent.trim() || 'N/A';
    //     const code = li.querySelector('div.flex.flex-col.sm\\:flex-row .flex.items-center > div')?.textContent.trim() || 'N/A';
    
    //     const deliveryContainer = li.querySelector('div.flex.items-left.mt-5'); // Or a more specific parent if needed
    
    //     // Now find the specific div with the text inside that container
    //     const deliveryEl = deliveryContainer?.querySelector('div.ml-1'); // Target the div with class ml-1
    //     const delivery = deliveryEl?.textContent.trim() || 'N/A'; // Get its text content

    
    //     items.push({
    //       name,
    //       brand,
    //       code,
    //       price,
    //       delivery_options: delivery,
    //       provider: 'MATEROM'
    //     });
    //   });
    
    //   return items;
    // }, RESULT_ITEMS_SELECTOR);
   
    // console.log(`📊 Scraped ${results.length} products.`);
    // console.log(JSON.stringify(results, null, 2)); // Output results

    // await browser.close();
    console.log(`✅ ${SCRAPER_NAME} scraper finished successfully.`);
    return [];

  } catch (err) {
    console.error(`❌ Error in ${SCRAPER_NAME}:`, err);
    if (browser) await browser.close();
    return null;
  }
}

module.exports = {
  runAjsScraper,
};

